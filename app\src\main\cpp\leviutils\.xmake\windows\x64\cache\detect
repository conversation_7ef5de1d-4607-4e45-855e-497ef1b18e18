{
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkver = "21"
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--shared"] = true,
            ["--tsaware"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-auto-import"] = true,
            ["--appcontainer"] = true,
            ["--strip-all"] = true,
            ["--disable-tsaware"] = true,
            ["-s"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--gc-sections"] = true,
            ["--high-entropy-va"] = true,
            ["--Bstatic"] = true,
            ["-S"] = true,
            ["-l"] = true,
            ["--export-all-symbols"] = true,
            ["--help"] = true,
            ["--large-address-aware"] = true,
            ["--fatal-warnings"] = true,
            ["--enable-auto-import"] = true,
            ["--strip-debug"] = true,
            ["-dy"] = true,
            ["--disable-dynamicbase"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-seh"] = true,
            ["-dn"] = true,
            ["--version"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--Bdynamic"] = true,
            ["--no-demangle"] = true,
            ["--insert-timestamp"] = true,
            ["-static"] = true,
            ["--disable-no-seh"] = true,
            ["-v"] = true,
            ["--no-whole-archive"] = true,
            ["--nxcompat"] = true,
            ["-L"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-nxcompat"] = true,
            ["--dynamicbase"] = true,
            ["--demangle"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--no-dynamicbase"] = true,
            ["--verbose"] = true,
            ["--no-insert-timestamp"] = true,
            ["--whole-archive"] = true,
            ["-m"] = true,
            ["--kill-at"] = true,
            ["--no-gc-sections"] = true,
            ["-o"] = true,
            ["--exclude-all-symbols"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fhip-new-launch-api"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["--analyze"] = true,
            ["-ffixed-x29"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fdata-sections"] = true,
            ["-MG"] = true,
            ["--emit-static-lib"] = true,
            ["-ffixed-d0"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fstack-protector-strong"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mno-packets"] = true,
            ["-mrelax"] = true,
            ["-imacros"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fxray-instrument"] = true,
            ["-ffunction-sections"] = true,
            ["-MJ"] = true,
            ["-mno-madd4"] = true,
            ["-I-"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-long-calls"] = true,
            ["-dM"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fms-hotpatch"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fobjc-arc"] = true,
            ["-fwritable-strings"] = true,
            ["-fmemory-profile"] = true,
            ["-finline-hint-functions"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fno-builtin"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fsized-deallocation"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-H"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-ffixed-x6"] = true,
            ["-femulated-tls"] = true,
            ["-trigraphs"] = true,
            ["-print-multiarch"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-freciprocal-math"] = true,
            ["-fnew-infallible"] = true,
            ["-mllvm"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-include"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-ffixed-r19"] = true,
            ["-fsystem-module"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-relocatable-pch"] = true,
            ["-fno-access-control"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-print-search-dirs"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffreestanding"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-temp-file"] = true,
            ["-mmadd4"] = true,
            ["--migrate"] = true,
            ["-nohipwrapperinc"] = true,
            ["-ffixed-a0"] = true,
            ["-Wdeprecated"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fno-split-stack"] = true,
            ["-fpcc-struct-return"] = true,
            ["-ffixed-d3"] = true,
            ["-isystem-after"] = true,
            ["-menable-experimental-extensions"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-integrated-as"] = true,
            ["-nogpulib"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-mmsa"] = true,
            ["-fsigned-char"] = true,
            ["-mno-gpopt"] = true,
            ["-gline-tables-only"] = true,
            ["-cl-mad-enable"] = true,
            ["-fno-debug-macro"] = true,
            ["-emit-ast"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-x24"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fno-lto"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-mnvj"] = true,
            ["-fgnu-keywords"] = true,
            ["-Xclang"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-d6"] = true,
            ["-fstack-usage"] = true,
            ["-static-libsan"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["--analyzer-output"] = true,
            ["-fno-spell-checking"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mbackchain"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fgnu89-inline"] = true,
            ["-fdebug-types-section"] = true,
            ["-ffixed-d7"] = true,
            ["-fxray-link-deps"] = true,
            ["-fapple-kext"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ivfsoverlay"] = true,
            ["-Xpreprocessor"] = true,
            ["-mcode-object-v3"] = true,
            ["-B"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffixed-x25"] = true,
            ["-fno-show-source-location"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fblocks"] = true,
            ["-traditional-cpp"] = true,
            ["-ffixed-x20"] = true,
            ["-mibt-seal"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-gdwarf-3"] = true,
            ["-pipe"] = true,
            ["-gline-directives-only"] = true,
            ["-fms-extensions"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-funroll-loops"] = true,
            ["-gembed-source"] = true,
            ["-fsycl"] = true,
            ["-mno-hvx"] = true,
            ["-mno-implicit-float"] = true,
            ["-foffload-lto"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-CC"] = true,
            ["-mgpopt"] = true,
            ["-fglobal-isel"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-msoft-float"] = true,
            ["-isysroot"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-trigraphs"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-b"] = true,
            ["-print-resource-dir"] = true,
            ["-fno-cxx-modules"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mlong-double-64"] = true,
            ["-mlvi-cfi"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fno-global-isel"] = true,
            ["-fasync-exceptions"] = true,
            ["-print-supported-cpus"] = true,
            ["-Xanalyzer"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-mno-nvj"] = true,
            ["-mno-global-merge"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-elide-type"] = true,
            ["-fstack-clash-protection"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcommon"] = true,
            ["-fcall-saved-x14"] = true,
            ["-msave-restore"] = true,
            ["-ffixed-x27"] = true,
            ["-mno-relax"] = true,
            ["-cl-opt-disable"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["--help-hidden"] = true,
            ["-module-file-info"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-fixed-point"] = true,
            ["--cuda-device-only"] = true,
            ["-fembed-bitcode"] = true,
            ["-ffinite-loops"] = true,
            ["-fmodules-search-all"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-digraphs"] = true,
            ["-fno-declspec"] = true,
            ["-fstandalone-debug"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-ffixed-x12"] = true,
            ["-finstrument-functions"] = true,
            ["-freroll-loops"] = true,
            ["-frwpi"] = true,
            ["-gdwarf-2"] = true,
            ["--config"] = true,
            ["-fmath-errno"] = true,
            ["-fjump-tables"] = true,
            ["-iwithsysroot"] = true,
            ["-S"] = true,
            ["-finline-functions"] = true,
            ["-mno-code-object-v3"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-ffixed-x18"] = true,
            ["-ffixed-x9"] = true,
            ["-dependency-file"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fstack-protector-all"] = true,
            ["-Qunused-arguments"] = true,
            ["-mno-outline"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-v"] = true,
            ["-ffixed-x5"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-T"] = true,
            ["-mtgsplit"] = true,
            ["-iwithprefix"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fno-jump-tables"] = true,
            ["-miamcu"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-nobuiltininc"] = true,
            ["-fshort-enums"] = true,
            ["-ffixed-a1"] = true,
            ["-index-header-map"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-ffixed-x19"] = true,
            ["-fcxx-modules"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-ffixed-r9"] = true,
            ["-pg"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-autolink"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-mglobal-merge"] = true,
            ["-shared-libsan"] = true,
            ["-MD"] = true,
            ["-membedded-data"] = true,
            ["-fkeep-static-consts"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-MMD"] = true,
            ["-MT"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-strict-return"] = true,
            ["-print-effective-triple"] = true,
            ["-z"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fcall-saved-x13"] = true,
            ["--no-cuda-version-check"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fstrict-enums"] = true,
            ["-working-directory"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-mmt"] = true,
            ["-fintegrated-as"] = true,
            ["-mrtd"] = true,
            ["-fno-xray-function-index"] = true,
            ["-ffixed-x26"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fmerge-all-constants"] = true,
            ["-mcmse"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-idirafter"] = true,
            ["-ftrigraphs"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fno-show-column"] = true,
            ["-fprofile-generate"] = true,
            ["-fexceptions"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fmodules"] = true,
            ["-ffixed-x10"] = true,
            ["-M"] = true,
            ["-fcall-saved-x8"] = true,
            ["-include-pch"] = true,
            ["-mabicalls"] = true,
            ["-isystem"] = true,
            ["-fmodules-ts"] = true,
            ["-MV"] = true,
            ["-gdwarf"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fansi-escape-codes"] = true,
            ["-emit-module"] = true,
            ["-fno-addrsig"] = true,
            ["-nostdinc"] = true,
            ["-maix-struct-return"] = true,
            ["-G"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fpascal-strings"] = true,
            ["-save-stats"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fapprox-func"] = true,
            ["-malign-double"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-memory-profile"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-gmodules"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-cl-no-stdinc"] = true,
            ["--hip-link"] = true,
            ["-mstackrealign"] = true,
            ["-fseh-exceptions"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-common"] = true,
            ["-extract-api"] = true,
            ["-mno-embedded-data"] = true,
            ["-mno-seses"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mfp32"] = true,
            ["-static-openmp"] = true,
            ["-MM"] = true,
            ["-fborland-extensions"] = true,
            ["-F"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-mnocrc"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-exceptions"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-ffixed-a4"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-use-init-array"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fpch-debuginfo"] = true,
            ["--verify-debug-info"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mlong-double-128"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-a3"] = true,
            ["-fstack-size-section"] = true,
            ["-flto"] = true,
            ["-fconvergent-functions"] = true,
            ["-fsave-optimization-record"] = true,
            ["-save-temps"] = true,
            ["-mno-msa"] = true,
            ["-ffixed-x13"] = true,
            ["-mnvs"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-print-targets"] = true,
            ["-module-dependency-dir"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-moutline"] = true,
            ["-ffixed-x16"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fsanitize-trap"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-verify-pch"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-mno-nvs"] = true,
            ["-dependency-dot"] = true,
            ["-mstack-arg-probe"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-L"] = true,
            ["-dD"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-x17"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-d2"] = true,
            ["-MF"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-print-runtime-dir"] = true,
            ["-Tdata"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fpch-codegen"] = true,
            ["-fshort-wchar"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fopenmp-simd"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-ibuiltininc"] = true,
            ["-fuse-line-directives"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fcall-saved-x9"] = true,
            ["-munaligned-access"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-offload-lto"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-ffixed-x8"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-new-infallible"] = true,
            ["-fwasm-exceptions"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fcxx-exceptions"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-rewrite-objc"] = true,
            ["-nogpuinc"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-ffixed-x21"] = true,
            ["-femit-all-decls"] = true,
            ["-mseses"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-mno-save-restore"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fno-short-wchar"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-sycl"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-gdwarf-4"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-profile-generate"] = true,
            ["-mcrc"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-rpath"] = true,
            ["-Xassembler"] = true,
            ["-x"] = true,
            ["-mfentry"] = true,
            ["-cxx-isystem"] = true,
            ["-mhvx"] = true,
            ["-Ttext"] = true,
            ["-gdwarf-5"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-ffixed-x3"] = true,
            ["-ffixed-d5"] = true,
            ["-help"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fprotect-parens"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fropi"] = true,
            ["-fverbose-asm"] = true,
            ["-fno-rtti-data"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-elide-constructors"] = true,
            ["-ffixed-d1"] = true,
            ["--start-no-unused-arguments"] = true,
            ["--precompile"] = true,
            ["-fgpu-rdc"] = true,
            ["-fno-plt"] = true,
            ["-freg-struct-return"] = true,
            ["-fvectorize"] = true,
            ["-ffixed-d4"] = true,
            ["-fzvector"] = true,
            ["-ffast-math"] = true,
            ["-fignore-exceptions"] = true,
            ["-dI"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-c"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fcf-protection"] = true,
            ["-emit-llvm"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-arch"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fopenmp"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-mno-tgsplit"] = true,
            ["-dsym-dir"] = true,
            ["-mextern-sdata"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-mno-mt"] = true,
            ["-time"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fapplication-extension"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fdeclspec"] = true,
            ["-mpacked-stack"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-P"] = true,
            ["-mmark-bti-property"] = true,
            ["-mno-cumode"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-unique-section-names"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-ffixed-a2"] = true,
            ["-E"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-Xopenmp-target"] = true,
            ["-w"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-ffixed-point"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fsplit-stack"] = true,
            ["-fopenmp-extensions"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fenable-matrix"] = true,
            ["-mrecord-mcount"] = true,
            ["-gdwarf32"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fms-compatibility"] = true,
            ["-ffixed-x22"] = true,
            ["-ffixed-a6"] = true,
            ["-mnop-mcount"] = true,
            ["-gdwarf64"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-finite-loops"] = true,
            ["-mms-bitfields"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-faddrsig"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-operator-names"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-ffixed-x30"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fdebug-macro"] = true,
            ["-ffixed-x23"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mfp64"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mno-restrict-it"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-Tbss"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-U"] = true,
            ["-MQ"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-signed-char"] = true,
            ["-fdigraphs"] = true,
            ["-ffixed-x28"] = true,
            ["-mlong-calls"] = true,
            ["-iprefix"] = true,
            ["-Qy"] = true,
            ["-Qn"] = true,
            ["-pthread"] = true,
            ["-gcodeview"] = true,
            ["-C"] = true,
            ["-ffixed-x2"] = true,
            ["-print-target-triple"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fstack-protector"] = true,
            ["-fobjc-exceptions"] = true,
            ["-mlocal-sdata"] = true,
            ["-mrelax-all"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-signed-zeros"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mmemops"] = true,
            ["-mthread-model"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mskip-rax-setup"] = true,
            ["-meabi"] = true,
            ["-o"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-stack-protector"] = true,
            ["-g"] = true,
            ["-undef"] = true,
            ["-mlvi-hardening"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fno-rtti"] = true,
            ["-mno-memops"] = true,
            ["-fno-unroll-loops"] = true,
            ["-moutline-atomics"] = true,
            ["-pedantic"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-D"] = true,
            ["-mno-crc"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fcs-profile-generate"] = true,
            ["-I"] = true,
            ["--cuda-host-only"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["--version"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fcoverage-mapping"] = true,
            ["-mno-local-sdata"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-ftime-trace"] = true,
            ["-ftrapv"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mpackets"] = true,
            ["-Xlinker"] = true,
            ["-MP"] = true,
            ["-mno-outline-atomics"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fmodules-decluse"] = true,
            ["-iquote"] = true,
            ["-mcumode"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-movt"] = true,
            ["-arcmt-migrate-emit-errors"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    }
}