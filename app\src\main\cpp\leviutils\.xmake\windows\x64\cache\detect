{
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            cross = "arm-linux-androideabi-",
            sdkver = "21",
            ndkver = 25,
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-ftrapv"] = true,
            ["-mno-abicalls"] = true,
            ["-fsave-optimization-record"] = true,
            ["-finline-hint-functions"] = true,
            ["--version"] = true,
            ["-relocatable-pch"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fms-extensions"] = true,
            ["-mnvs"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fverbose-asm"] = true,
            ["-z"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fstack-size-section"] = true,
            ["-pipe"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-x27"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-new-infallible"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-mextern-sdata"] = true,
            ["-mlong-double-80"] = true,
            ["-Xpreprocessor"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-mrtd"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-MMD"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-ffixed-x14"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-print-targets"] = true,
            ["-ibuiltininc"] = true,
            ["-mno-save-restore"] = true,
            ["--cuda-host-only"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-MV"] = true,
            ["-ffixed-x10"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fopenmp-simd"] = true,
            ["-x"] = true,
            ["-fblocks"] = true,
            ["-idirafter"] = true,
            ["-fno-strict-return"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-mfentry"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-mrelax"] = true,
            ["-fropi"] = true,
            ["-mlvi-cfi"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-x29"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-w"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mno-relax"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fms-hotpatch"] = true,
            ["-msoft-float"] = true,
            ["-fno-discard-value-names"] = true,
            ["-ffixed-x21"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-static-libsan"] = true,
            ["-mno-memops"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-builtin"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-common"] = true,
            ["-mno-cumode"] = true,
            ["-Xclang"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mmemops"] = true,
            ["-ffunction-sections"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffixed-r9"] = true,
            ["-fms-compatibility"] = true,
            ["-mlocal-sdata"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fmodules-decluse"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-integrated-as"] = true,
            ["-MP"] = true,
            ["-fstandalone-debug"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffixed-d0"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mno-mt"] = true,
            ["-emit-module"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-mseses"] = true,
            ["-fno-use-init-array"] = true,
            ["-fapplication-extension"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fcoroutines-ts"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-ffixed-x6"] = true,
            ["-fdata-sections"] = true,
            ["-fno-lto"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-gcodeview"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-dependency-file"] = true,
            ["-mno-execute-only"] = true,
            ["-gdwarf-3"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-ffixed-x3"] = true,
            ["-fapprox-func"] = true,
            ["-MQ"] = true,
            ["-emit-ast"] = true,
            ["-ffixed-x19"] = true,
            ["-P"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mhvx-qfloat"] = true,
            ["-faligned-allocation"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fsized-deallocation"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mno-nvs"] = true,
            ["-fstack-protector-all"] = true,
            ["-fstack-protector"] = true,
            ["--precompile"] = true,
            ["-foffload-lto"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mmark-bti-property"] = true,
            ["-nogpuinc"] = true,
            ["-fstack-usage"] = true,
            ["-Ttext"] = true,
            ["-finstrument-functions"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fopenmp"] = true,
            ["-fintegrated-as"] = true,
            ["-CC"] = true,
            ["--emit-static-lib"] = true,
            ["-o"] = true,
            ["-ffixed-x28"] = true,
            ["-mfp64"] = true,
            ["-fmerge-all-constants"] = true,
            ["-dD"] = true,
            ["-fseh-exceptions"] = true,
            ["-fmath-errno"] = true,
            ["-ffixed-x24"] = true,
            ["-fsystem-module"] = true,
            ["-Tdata"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-mlong-calls"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fignore-exceptions"] = true,
            ["-iprefix"] = true,
            ["-trigraphs"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fno-profile-generate"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-spell-checking"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["--cuda-device-only"] = true,
            ["-MG"] = true,
            ["-fnew-infallible"] = true,
            ["-mms-bitfields"] = true,
            ["-mlong-double-128"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-gdwarf-2"] = true,
            ["-fopenmp-extensions"] = true,
            ["-ffixed-x15"] = true,
            ["-v"] = true,
            ["-funroll-loops"] = true,
            ["-fkeep-static-consts"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-mno-tgsplit"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-ffixed-a4"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fno-access-control"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-msvr4-struct-return"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fsigned-char"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fvectorize"] = true,
            ["-mtgsplit"] = true,
            ["-ffixed-x7"] = true,
            ["-fzvector"] = true,
            ["-ffixed-a2"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-x9"] = true,
            ["--no-cuda-version-check"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-trigraphs"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-memory-profile"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fcommon"] = true,
            ["-U"] = true,
            ["-nogpulib"] = true,
            ["-ffixed-d5"] = true,
            ["-fwasm-exceptions"] = true,
            ["-MM"] = true,
            ["-fno-sycl"] = true,
            ["-moutline"] = true,
            ["-mrecord-mcount"] = true,
            ["-H"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fobjc-exceptions"] = true,
            ["-MD"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-iwithprefix"] = true,
            ["-extract-api"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-ffixed-x12"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-iquote"] = true,
            ["-G"] = true,
            ["-ffixed-d1"] = true,
            ["-freg-struct-return"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["--migrate"] = true,
            ["-mno-code-object-v3"] = true,
            ["-ffinite-loops"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mabicalls"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-gcodeview-ghash"] = true,
            ["-gdwarf32"] = true,
            ["-g"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mrelax-all"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-Qy"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-autolink"] = true,
            ["-mlvi-hardening"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-isystem-after"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-stack-protector"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-ffixed-x13"] = true,
            ["-isystem"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fno-pch-codegen"] = true,
            ["-I"] = true,
            ["-Wdeprecated"] = true,
            ["-cl-opt-disable"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["--help-hidden"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fmodules-search-all"] = true,
            ["-fenable-matrix"] = true,
            ["-ffixed-d4"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mnocrc"] = true,
            ["-mexecute-only"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mno-extern-sdata"] = true,
            ["-L"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fobjc-arc"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-x26"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fborland-extensions"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mcrc"] = true,
            ["-static-openmp"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-MJ"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-pg"] = true,
            ["-I-"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mno-global-merge"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-cxx-modules"] = true,
            ["-print-multiarch"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fsanitize-trap"] = true,
            ["-fsplit-stack"] = true,
            ["-fcf-protection"] = true,
            ["-MF"] = true,
            ["-mno-packets"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-rtti"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-Tbss"] = true,
            ["-mthread-model"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fmodules"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-Xassembler"] = true,
            ["-undef"] = true,
            ["-fstrict-enums"] = true,
            ["-shared-libsan"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-freciprocal-math"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-signed-char"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-operator-names"] = true,
            ["-mgpopt"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-meabi"] = true,
            ["-help"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fstack-protector-strong"] = true,
            ["-ffixed-x2"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-ivfsoverlay"] = true,
            ["-ffixed-x1"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mskip-rax-setup"] = true,
            ["-print-ivar-layout"] = true,
            ["-ffixed-a5"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-M"] = true,
            ["-mmsa"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fconvergent-functions"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-mnvj"] = true,
            ["-fglobal-isel"] = true,
            ["-fapple-kext"] = true,
            ["-fcall-saved-x13"] = true,
            ["-pthread"] = true,
            ["-mcumode"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fdebug-types-section"] = true,
            ["--analyzer-output"] = true,
            ["-fexceptions"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fgpu-rdc"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-T"] = true,
            ["--config"] = true,
            ["-ffixed-d6"] = true,
            ["-cl-mad-enable"] = true,
            ["-mglobal-merge"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fshort-wchar"] = true,
            ["-fobjc-weak"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-print-runtime-dir"] = true,
            ["-miamcu"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-dependency-dot"] = true,
            ["-ffixed-a3"] = true,
            ["-gdwarf64"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-gdwarf-5"] = true,
            ["-b"] = true,
            ["-isysroot"] = true,
            ["-fpch-codegen"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-print-search-dirs"] = true,
            ["-ffixed-x17"] = true,
            ["-dI"] = true,
            ["-fcxx-modules"] = true,
            ["-save-temps"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-mfp32"] = true,
            ["-fno-debug-macro"] = true,
            ["-ftrigraphs"] = true,
            ["-maix-struct-return"] = true,
            ["-mmt"] = true,
            ["-ffixed-x23"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-femit-all-decls"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-F"] = true,
            ["-S"] = true,
            ["-ffixed-x8"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-gpopt"] = true,
            ["-print-supported-cpus"] = true,
            ["-ffast-math"] = true,
            ["-ffixed-x22"] = true,
            ["-faddrsig"] = true,
            ["-fno-show-source-location"] = true,
            ["-include-pch"] = true,
            ["-fno-unroll-loops"] = true,
            ["-ffixed-r19"] = true,
            ["-malign-double"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mno-msa"] = true,
            ["-mpacked-stack"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mhvx"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-finite-loops"] = true,
            ["-mno-nvj"] = true,
            ["-ffixed-x25"] = true,
            ["-mno-restrict-it"] = true,
            ["-print-target-triple"] = true,
            ["-freroll-loops"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["--verify-debug-info"] = true,
            ["-traditional-cpp"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mllvm"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mcmse"] = true,
            ["-fgnu-runtime"] = true,
            ["-femulated-tls"] = true,
            ["-c"] = true,
            ["-moutline-atomics"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-a6"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mno-long-calls"] = true,
            ["-mpackets"] = true,
            ["-save-stats"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-rpath"] = true,
            ["-msave-restore"] = true,
            ["-mbackchain"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mno-implicit-float"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-B"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fshort-enums"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-short-wchar"] = true,
            ["-include"] = true,
            ["-frwpi"] = true,
            ["-mlong-double-64"] = true,
            ["-ffixed-x30"] = true,
            ["-fsycl"] = true,
            ["-Qn"] = true,
            ["-Xlinker"] = true,
            ["-finline-functions"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fdeclspec"] = true,
            ["-fgnu89-inline"] = true,
            ["-ffixed-d3"] = true,
            ["-imacros"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fdebug-macro"] = true,
            ["-gline-directives-only"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mno-crc"] = true,
            ["-time"] = true,
            ["-ffixed-d2"] = true,
            ["-mno-hvx"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fslp-vectorize"] = true,
            ["-fcxx-exceptions"] = true,
            ["-gline-tables-only"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mno-embedded-data"] = true,
            ["-membedded-data"] = true,
            ["-fno-exceptions"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-pedantic"] = true,
            ["-D"] = true,
            ["-gmodules"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-iwithsysroot"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-jump-tables"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-print-resource-dir"] = true,
            ["-fintegrated-cc1"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-Xopenmp-target"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fembed-bitcode"] = true,
            ["-fno-digraphs"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-module-dependency-dir"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-ffixed-x18"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ftime-trace"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-show-column"] = true,
            ["-fno-addrsig"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fforce-enable-int128"] = true,
            ["-gdwarf-4"] = true,
            ["-fuse-line-directives"] = true,
            ["-mno-outline"] = true,
            ["-C"] = true,
            ["-fasync-exceptions"] = true,
            ["-munaligned-access"] = true,
            ["-fmemory-profile"] = true,
            ["-Xanalyzer"] = true,
            ["-flto"] = true,
            ["-dsym-dir"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-ffixed-x5"] = true,
            ["-print-effective-triple"] = true,
            ["-ffreestanding"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-temp-file"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-mno-madd4"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-Qunused-arguments"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-verify-pch"] = true,
            ["-mno-seses"] = true,
            ["-emit-llvm"] = true,
            ["-emit-interface-stubs"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-plt"] = true,
            ["-fprofile-generate"] = true,
            ["-mibt-seal"] = true,
            ["-gdwarf"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fxray-link-deps"] = true,
            ["-nostdinc"] = true,
            ["-gembed-source"] = true,
            ["-mno-movt"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fpascal-strings"] = true,
            ["-dM"] = true,
            ["-fno-rtti-data"] = true,
            ["--hip-link"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fprotect-parens"] = true,
            ["-arch"] = true,
            ["-mstackrealign"] = true,
            ["-fxray-instrument"] = true,
            ["-fjump-tables"] = true,
            ["-fdigraphs"] = true,
            ["-E"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fno-offload-lto"] = true,
            ["-fsanitize-stats"] = true,
            ["-index-header-map"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-ffixed-x20"] = true,
            ["-mmadd4"] = true,
            ["-ffixed-x11"] = true,
            ["-module-file-info"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-split-stack"] = true,
            ["--analyze"] = true,
            ["-mcode-object-v3"] = true,
            ["-ffixed-point"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-MT"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-nobuiltininc"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-declspec"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-gno-embed-source"] = true,
            ["-working-directory"] = true
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--high-entropy-va"] = true,
            ["--disable-auto-import"] = true,
            ["--nxcompat"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--strip-all"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-whole-archive"] = true,
            ["--insert-timestamp"] = true,
            ["--tsaware"] = true,
            ["-static"] = true,
            ["--no-dynamicbase"] = true,
            ["-v"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--export-all-symbols"] = true,
            ["--verbose"] = true,
            ["--gc-sections"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-tsaware"] = true,
            ["-dn"] = true,
            ["--fatal-warnings"] = true,
            ["--help"] = true,
            ["--demangle"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--shared"] = true,
            ["--version"] = true,
            ["--large-address-aware"] = true,
            ["-l"] = true,
            ["-S"] = true,
            ["--Bstatic"] = true,
            ["--exclude-all-symbols"] = true,
            ["--appcontainer"] = true,
            ["--whole-archive"] = true,
            ["-L"] = true,
            ["-dy"] = true,
            ["-o"] = true,
            ["-s"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-no-seh"] = true,
            ["--kill-at"] = true,
            ["--enable-auto-import"] = true,
            ["--no-seh"] = true,
            ["--disable-nxcompat"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-m"] = true,
            ["--no-demangle"] = true,
            ["--dynamicbase"] = true,
            ["--Bdynamic"] = true,
            ["--strip-debug"] = true,
            ["--no-fatal-warnings"] = true
        }
    }
}