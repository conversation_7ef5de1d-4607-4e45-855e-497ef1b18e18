{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--allow-multiple-definition"] = true,
            ["--disable-no-seh"] = true,
            ["-l"] = true,
            ["--demangle"] = true,
            ["--verbose"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-auto-import"] = true,
            ["-L"] = true,
            ["--fatal-warnings"] = true,
            ["--no-seh"] = true,
            ["-static"] = true,
            ["--disable-nxcompat"] = true,
            ["--no-fatal-warnings"] = true,
            ["--nxcompat"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-tsaware"] = true,
            ["--high-entropy-va"] = true,
            ["-dy"] = true,
            ["--no-insert-timestamp"] = true,
            ["--strip-all"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-dn"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-o"] = true,
            ["-s"] = true,
            ["--help"] = true,
            ["-m"] = true,
            ["--dynamicbase"] = true,
            ["--large-address-aware"] = true,
            ["--exclude-all-symbols"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-dynamicbase"] = true,
            ["--version"] = true,
            ["--appcontainer"] = true,
            ["--kill-at"] = true,
            ["--strip-debug"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--enable-auto-import"] = true,
            ["-S"] = true,
            ["--whole-archive"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-demangle"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--Bdynamic"] = true,
            ["-v"] = true,
            ["--tsaware"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--gc-sections"] = true,
            ["--no-gc-sections"] = true,
            ["--Bstatic"] = true,
            ["--shared"] = true,
            ["--no-dynamicbase"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mqdsp6-compat"] = true,
            ["-munaligned-access"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-cl-no-stdinc"] = true,
            ["-dI"] = true,
            ["-fno-strict-return"] = true,
            ["-mbackchain"] = true,
            ["-trigraphs"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fborland-extensions"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-finline-functions"] = true,
            ["-fxray-instrument"] = true,
            ["-E"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fstack-size-section"] = true,
            ["-ffixed-x31"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mcmse"] = true,
            ["-mabicalls"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-autolink"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fxray-link-deps"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fno-common"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-rpath"] = true,
            ["-ffixed-x19"] = true,
            ["-mhvx"] = true,
            ["-fshort-wchar"] = true,
            ["-fuse-line-directives"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-mcumode"] = true,
            ["-fno-access-control"] = true,
            ["-idirafter"] = true,
            ["-mno-gpopt"] = true,
            ["--gpu-bundle-output"] = true,
            ["-ffixed-x28"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-nobuiltininc"] = true,
            ["-mno-memops"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-lto"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-mno-mt"] = true,
            ["-c"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-G"] = true,
            ["-fpascal-strings"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-iquote"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-P"] = true,
            ["-fcall-saved-x14"] = true,
            ["-ffixed-x3"] = true,
            ["-mno-local-sdata"] = true,
            ["-fcall-saved-x15"] = true,
            ["--hip-link"] = true,
            ["-mlong-double-64"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-ffixed-x9"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mfp64"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-mcode-object-v3"] = true,
            ["-mno-global-merge"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fnew-infallible"] = true,
            ["-mwavefrontsize64"] = true,
            ["-faddrsig"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fsycl"] = true,
            ["-fropi"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fsanitize-stats"] = true,
            ["-print-resource-dir"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-frwpi"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-include-pch"] = true,
            ["-MD"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["--help-hidden"] = true,
            ["-ffixed-x14"] = true,
            ["--analyzer-output"] = true,
            ["-B"] = true,
            ["-arch"] = true,
            ["-fno-spell-checking"] = true,
            ["-isystem-after"] = true,
            ["-fno-jump-tables"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-stack-protector"] = true,
            ["-gdwarf-5"] = true,
            ["-fdeclspec"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fignore-exceptions"] = true,
            ["-o"] = true,
            ["-iwithprefixbefore"] = true,
            ["-C"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-ffixed-x26"] = true,
            ["-verify-pch"] = true,
            ["-mextern-sdata"] = true,
            ["-ffixed-d4"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-emit-llvm"] = true,
            ["-mpackets"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-mmadd4"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-signed-char"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fopenmp-simd"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fms-compatibility"] = true,
            ["-nogpuinc"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-moutline-atomics"] = true,
            ["-dsym-dir"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fms-hotpatch"] = true,
            ["-ffixed-d2"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fmodules-search-all"] = true,
            ["-mno-outline-atomics"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fno-rtti"] = true,
            ["-fmemory-profile"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mno-save-restore"] = true,
            ["-mms-bitfields"] = true,
            ["-static-openmp"] = true,
            ["-emit-interface-stubs"] = true,
            ["-ffixed-x8"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-ffixed-x20"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-ftrapv"] = true,
            ["-freciprocal-math"] = true,
            ["-fdigraphs"] = true,
            ["-CC"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fapple-link-rtlib"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fno-new-infallible"] = true,
            ["-ffixed-x12"] = true,
            ["-mrecord-mcount"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-finline-hint-functions"] = true,
            ["-fconvergent-functions"] = true,
            ["-include"] = true,
            ["-MT"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-x17"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fpch-codegen"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fprotect-parens"] = true,
            ["-H"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-Qn"] = true,
            ["-nogpulib"] = true,
            ["-gline-tables-only"] = true,
            ["-ffixed-x23"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fsplit-stack"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-print-search-dirs"] = true,
            ["--cuda-device-only"] = true,
            ["-fseh-exceptions"] = true,
            ["-mno-embedded-data"] = true,
            ["-femulated-tls"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fcoverage-mapping"] = true,
            ["-I"] = true,
            ["-fintegrated-as"] = true,
            ["-fslp-vectorize"] = true,
            ["-gmodules"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mno-nvj"] = true,
            ["-fintegrated-cc1"] = true,
            ["-freroll-loops"] = true,
            ["-mno-extern-sdata"] = true,
            ["-shared-libsan"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fcxx-modules"] = true,
            ["-fno-show-column"] = true,
            ["-iwithsysroot"] = true,
            ["-mno-implicit-float"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-mmt"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-gdwarf64"] = true,
            ["-g"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fenable-matrix"] = true,
            ["-flto"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-madd4"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mno-hvx"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-fixed-point"] = true,
            ["--emit-static-lib"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-debug-macro"] = true,
            ["-ffixed-d3"] = true,
            ["-fapprox-func"] = true,
            ["-miamcu"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-serialize-diagnostics"] = true,
            ["-working-directory"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fgpu-rdc"] = true,
            ["-mexecute-only"] = true,
            ["-ffast-math"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-global-isel"] = true,
            ["-ffixed-a6"] = true,
            ["-ffixed-x6"] = true,
            ["-relocatable-pch"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-ffunction-sections"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-mtgsplit"] = true,
            ["-U"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-x29"] = true,
            ["-ftrigraphs"] = true,
            ["-v"] = true,
            ["-traditional-cpp"] = true,
            ["-MJ"] = true,
            ["-fsigned-char"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-funroll-loops"] = true,
            ["-ffixed-x10"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-cxx-modules"] = true,
            ["-T"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fembed-bitcode"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-cl-mad-enable"] = true,
            ["-module-dependency-dir"] = true,
            ["-mlong-calls"] = true,
            ["-ffixed-x13"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-S"] = true,
            ["-pipe"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-I-"] = true,
            ["-fobjc-arc"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-d7"] = true,
            ["-gdwarf"] = true,
            ["-imacros"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-ffixed-a0"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fgnu-runtime"] = true,
            ["-ibuiltininc"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-sycl"] = true,
            ["-ftime-trace"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-Xclang"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-Qy"] = true,
            ["-fobjc-weak"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-dependency-dot"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-addrsig"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-Wdeprecated"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fapple-kext"] = true,
            ["-Xlinker"] = true,
            ["-index-header-map"] = true,
            ["-ffixed-x2"] = true,
            ["--analyze"] = true,
            ["-fasync-exceptions"] = true,
            ["-fms-extensions"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-isystem"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fsystem-module"] = true,
            ["-fwasm-exceptions"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-mno-restrict-it"] = true,
            ["-ffixed-x22"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fstrict-enums"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-split-stack"] = true,
            ["-mnvs"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mlocal-sdata"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-mrtd"] = true,
            ["-ffixed-x11"] = true,
            ["-extract-api"] = true,
            ["-fno-memory-profile"] = true,
            ["-Xanalyzer"] = true,
            ["-pthread"] = true,
            ["-nostdinc"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-Tbss"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fmath-errno"] = true,
            ["-Xassembler"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fmodules"] = true,
            ["-fsized-deallocation"] = true,
            ["-fforce-enable-int128"] = true,
            ["--cuda-host-only"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-digraphs"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-trigraphs"] = true,
            ["-fno-show-source-location"] = true,
            ["-fvectorize"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-MM"] = true,
            ["-mno-nvs"] = true,
            ["-print-effective-triple"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fno-short-wchar"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-ffixed-x25"] = true,
            ["-maix-struct-return"] = true,
            ["-mlvi-hardening"] = true,
            ["-time"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-rewrite-objc"] = true,
            ["-fjump-tables"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-mfentry"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-ffixed-x27"] = true,
            ["-fexceptions"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mstackrealign"] = true,
            ["-gdwarf-4"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mno-tgsplit"] = true,
            ["-mrestrict-it"] = true,
            ["-femit-all-decls"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-use-init-array"] = true,
            ["-gcodeview"] = true,
            ["-print-supported-cpus"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-integrated-as"] = true,
            ["-ffixed-d6"] = true,
            ["-ffixed-x24"] = true,
            ["-fshort-enums"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-save-temps"] = true,
            ["-fdebug-types-section"] = true,
            ["-pedantic"] = true,
            ["-ffixed-x5"] = true,
            ["-mskip-rax-setup"] = true,
            ["-cl-opt-disable"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-isysroot"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-exceptions"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fno-operator-names"] = true,
            ["-M"] = true,
            ["-Tdata"] = true,
            ["-mlong-double-128"] = true,
            ["-Xopenmp-target"] = true,
            ["-mno-unaligned-access"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-x21"] = true,
            ["--config"] = true,
            ["-malign-double"] = true,
            ["-mrelax-all"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-pch-codegen"] = true,
            ["-mlvi-cfi"] = true,
            ["-mno-seses"] = true,
            ["-fsanitize-trap"] = true,
            ["-L"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-z"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-gdwarf32"] = true,
            ["-nohipwrapperinc"] = true,
            ["-ivfsoverlay"] = true,
            ["-mcrc"] = true,
            ["-fmodules-ts"] = true,
            ["--version"] = true,
            ["-mno-movt"] = true,
            ["-dD"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-profile-generate"] = true,
            ["-ffixed-a3"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-mmemops"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-save-stats"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-iprefix"] = true,
            ["-fstack-protector-all"] = true,
            ["-MF"] = true,
            ["-D"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fgnu89-inline"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-foffload-lto"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fmodules-decluse"] = true,
            ["-fno-elide-type"] = true,
            ["-pg"] = true,
            ["-fstandalone-debug"] = true,
            ["-fverbose-asm"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-gdwarf-3"] = true,
            ["-print-multiarch"] = true,
            ["-mnvj"] = true,
            ["-gline-directives-only"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fblocks"] = true,
            ["-ffixed-d1"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-help"] = true,
            ["-freg-struct-return"] = true,
            ["-meabi"] = true,
            ["-MG"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-ffixed-point"] = true,
            ["-fno-rtti-data"] = true,
            ["-mno-outline"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-ffixed-a5"] = true,
            ["-dM"] = true,
            ["-membedded-data"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-emit-ast"] = true,
            ["-cxx-isystem"] = true,
            ["-module-file-info"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-finstrument-functions"] = true,
            ["-fstack-usage"] = true,
            ["-Ttext"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fcommon"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-emit-module"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fstack-protector"] = true,
            ["-mthread-model"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mno-crc"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-ffixed-r19"] = true,
            ["-fcf-protection"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-plt"] = true,
            ["-mrelax"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-a4"] = true,
            ["-faligned-allocation"] = true,
            ["-ffixed-x7"] = true,
            ["-fdata-sections"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mno-packets"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mglobal-merge"] = true,
            ["-fapplication-extension"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fopenmp"] = true,
            ["-fno-elide-constructors"] = true,
            ["-MV"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mpacked-stack"] = true,
            ["-mfp32"] = true,
            ["-fglobal-isel"] = true,
            ["-MMD"] = true,
            ["-fno-offload-lto"] = true,
            ["-gembed-source"] = true,
            ["-moutline"] = true,
            ["-mno-cumode"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-dependency-file"] = true,
            ["-fcall-saved-x18"] = true,
            ["-mmsa"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mnocrc"] = true,
            ["-iwithprefix"] = true,
            ["-print-target-triple"] = true,
            ["-fdebug-macro"] = true,
            ["-msave-restore"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-mno-msa"] = true,
            ["--precompile"] = true,
            ["-mseses"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-builtin"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-ffixed-x30"] = true,
            ["-ffixed-d5"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-print-targets"] = true,
            ["-static-libsan"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-ffreestanding"] = true,
            ["-w"] = true,
            ["-ffixed-x18"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-finite-loops"] = true,
            ["-undef"] = true,
            ["-fno-declspec"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fprofile-generate"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-mno-relax"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-mmark-bti-property"] = true,
            ["-MP"] = true,
            ["-mibt-seal"] = true,
            ["-fzvector"] = true,
            ["-gno-embed-source"] = true,
            ["-msoft-float"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-b"] = true,
            ["-cl-finite-math-only"] = true,
            ["-mllvm"] = true,
            ["-MQ"] = true,
            ["-mgpopt"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-ffixed-a1"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-x"] = true,
            ["-F"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["--no-cuda-version-check"] = true,
            ["-emit-merged-ifs"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}