{
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ibuiltininc"] = true,
            ["-fno-cxx-modules"] = true,
            ["-Tdata"] = true,
            ["-fopenmp-simd"] = true,
            ["-ffixed-x21"] = true,
            ["-mno-cumode"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-time"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fshort-wchar"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fwasm-exceptions"] = true,
            ["-iquote"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-dsym-dir"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-nogpuinc"] = true,
            ["-mms-bitfields"] = true,
            ["-Ttext"] = true,
            ["-mrelax-all"] = true,
            ["-ffixed-x13"] = true,
            ["-fmath-errno"] = true,
            ["-mcmse"] = true,
            ["-Wdeprecated"] = true,
            ["-fopenmp"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fcall-saved-x8"] = true,
            ["-MP"] = true,
            ["-ffast-math"] = true,
            ["-gdwarf"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mllvm"] = true,
            ["--hip-link"] = true,
            ["-fobjc-arc"] = true,
            ["-fcxx-exceptions"] = true,
            ["-gline-directives-only"] = true,
            ["-msoft-float"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mno-global-merge"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fembed-bitcode"] = true,
            ["-funroll-loops"] = true,
            ["-frwpi"] = true,
            ["-fcs-profile-generate"] = true,
            ["-mstackrealign"] = true,
            ["-mno-msa"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-ffixed-x1"] = true,
            ["-femit-all-decls"] = true,
            ["-faddrsig"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-short-wchar"] = true,
            ["-cl-finite-math-only"] = true,
            ["-ffixed-point"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-H"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-builtin"] = true,
            ["-fcoroutines-ts"] = true,
            ["--migrate"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fborland-extensions"] = true,
            ["--version"] = true,
            ["-iwithsysroot"] = true,
            ["-fno-integrated-as"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-pedantic"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-unroll-loops"] = true,
            ["-undef"] = true,
            ["-pg"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fjump-tables"] = true,
            ["-mmsa"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-mmark-bti-property"] = true,
            ["-fno-strict-return"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-moutline"] = true,
            ["-mno-nvs"] = true,
            ["-F"] = true,
            ["-dI"] = true,
            ["-mno-madd4"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-ffixed-x25"] = true,
            ["-fstack-size-section"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fno-jump-tables"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-I"] = true,
            ["-gmodules"] = true,
            ["-mno-code-object-v3"] = true,
            ["-freciprocal-math"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fexceptions"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-w"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fintegrated-cc1"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fgnu-keywords"] = true,
            ["-fobjc-weak"] = true,
            ["-Xanalyzer"] = true,
            ["-fapprox-func"] = true,
            ["-fcall-saved-x14"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-arch"] = true,
            ["-mno-embedded-data"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-common"] = true,
            ["-include-pch"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fxray-link-deps"] = true,
            ["-mno-movt"] = true,
            ["-mno-execute-only"] = true,
            ["-ffixed-x26"] = true,
            ["-mqdsp6-compat"] = true,
            ["-Tbss"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-ffixed-d2"] = true,
            ["-help"] = true,
            ["-mrtd"] = true,
            ["-fno-profile-generate"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-unique-section-names"] = true,
            ["-MD"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-d5"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-stack-protector"] = true,
            ["-Xlinker"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-iwithprefix"] = true,
            ["-fsycl"] = true,
            ["-fno-global-isel"] = true,
            ["-relocatable-pch"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-fixed-point"] = true,
            ["-U"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-use-init-array"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mseses"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fno-access-control"] = true,
            ["-mfp32"] = true,
            ["-idirafter"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mlvi-cfi"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-T"] = true,
            ["-dependency-dot"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fcall-saved-x12"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fno-elide-type"] = true,
            ["-mnop-mcount"] = true,
            ["-mnocrc"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-MG"] = true,
            ["-S"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-faligned-allocation"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-iwithprefixbefore"] = true,
            ["-finline-functions"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-abicalls"] = true,
            ["-mcode-object-v3"] = true,
            ["-ffixed-x19"] = true,
            ["-fno-autolink"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fapple-kext"] = true,
            ["-fglobal-isel"] = true,
            ["-fsanitize-trap"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-rtti-data"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x20"] = true,
            ["-isystem"] = true,
            ["-fpch-debuginfo"] = true,
            ["-isysroot"] = true,
            ["-ftrapv"] = true,
            ["-print-supported-cpus"] = true,
            ["-mexecute-only"] = true,
            ["-print-search-dirs"] = true,
            ["-fsized-deallocation"] = true,
            ["-ivfsoverlay"] = true,
            ["-ffixed-a3"] = true,
            ["-miamcu"] = true,
            ["-fno-spell-checking"] = true,
            ["-Xassembler"] = true,
            ["-fno-lto"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fmemory-profile"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-o"] = true,
            ["--precompile"] = true,
            ["-print-target-triple"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-ffixed-d3"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-sycl"] = true,
            ["-mfp64"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mlong-double-128"] = true,
            ["-fslp-vectorize"] = true,
            ["-MJ"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-ffixed-x10"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-ffixed-x17"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-dD"] = true,
            ["-mno-unaligned-access"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffixed-x29"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-mno-local-sdata"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffixed-x4"] = true,
            ["-mno-crc"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-mno-packets"] = true,
            ["-maix-struct-return"] = true,
            ["-index-header-map"] = true,
            ["-ffixed-x6"] = true,
            ["-MT"] = true,
            ["-freg-struct-return"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-new-infallible"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mno-gpopt"] = true,
            ["-M"] = true,
            ["-ffixed-d7"] = true,
            ["-fdigraphs"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-foffload-lto"] = true,
            ["-mno-mt"] = true,
            ["-fgnu89-inline"] = true,
            ["-Xopenmp-target"] = true,
            ["-fintegrated-as"] = true,
            ["-mfentry"] = true,
            ["-fno-show-column"] = true,
            ["-nohipwrapperinc"] = true,
            ["--config"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-ffixed-a6"] = true,
            ["-L"] = true,
            ["-moutline-atomics"] = true,
            ["-mno-restrict-it"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-ffixed-x22"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-a5"] = true,
            ["-Qunused-arguments"] = true,
            ["-pipe"] = true,
            ["-ffixed-r9"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mcrc"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fsigned-char"] = true,
            ["-fcf-protection"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fstandalone-debug"] = true,
            ["-mpackets"] = true,
            ["-ffixed-x5"] = true,
            ["-x"] = true,
            ["-mlvi-hardening"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-print-ivar-layout"] = true,
            ["-fignore-exceptions"] = true,
            ["-mhvx"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-pthread"] = true,
            ["-module-dependency-dir"] = true,
            ["-gline-tables-only"] = true,
            ["-fgpu-rdc"] = true,
            ["-save-stats"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-gdwarf-5"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-I-"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fvectorize"] = true,
            ["-P"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-malign-double"] = true,
            ["-fnew-infallible"] = true,
            ["-nogpulib"] = true,
            ["-fno-offload-lto"] = true,
            ["--analyzer-output"] = true,
            ["-working-directory"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-trigraphs"] = true,
            ["-mmt"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-addrsig"] = true,
            ["-Qy"] = true,
            ["-ffixed-x11"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-mtgsplit"] = true,
            ["-Qn"] = true,
            ["-iprefix"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-mno-tgsplit"] = true,
            ["-fmodules-decluse"] = true,
            ["-mno-relax"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fcommon"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-B"] = true,
            ["-module-file-info"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-v"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fcall-saved-x18"] = true,
            ["-membedded-data"] = true,
            ["-fprofile-generate"] = true,
            ["-ffixed-a0"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-nobuiltininc"] = true,
            ["-fverbose-asm"] = true,
            ["-fconvergent-functions"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-split-stack"] = true,
            ["-fmodules-search-all"] = true,
            ["-ffixed-a1"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-memory-profile"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-exceptions"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-gdwarf32"] = true,
            ["-print-resource-dir"] = true,
            ["-ffunction-sections"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["--cuda-device-only"] = true,
            ["-ffixed-x3"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ffixed-r19"] = true,
            ["-gdwarf-3"] = true,
            ["-ffixed-x9"] = true,
            ["-fmodules-ts"] = true,
            ["-fms-compatibility"] = true,
            ["-fpcc-struct-return"] = true,
            ["-MMD"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mglobal-merge"] = true,
            ["-munaligned-access"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fstack-protector"] = true,
            ["-fuse-line-directives"] = true,
            ["-ffixed-x24"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fforce-enable-int128"] = true,
            ["-ffixed-x7"] = true,
            ["-fstack-usage"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-dependency-file"] = true,
            ["-mno-hvx"] = true,
            ["-g"] = true,
            ["-traditional-cpp"] = true,
            ["-extract-api"] = true,
            ["-isystem-after"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fxray-instrument"] = true,
            ["-fansi-escape-codes"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-cl-mad-enable"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-rtti"] = true,
            ["-femulated-tls"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mabicalls"] = true,
            ["-ffixed-d4"] = true,
            ["-emit-module"] = true,
            ["-print-targets"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ftrigraphs"] = true,
            ["-fshort-enums"] = true,
            ["-freroll-loops"] = true,
            ["-ffixed-x18"] = true,
            ["-G"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-cxx-isystem"] = true,
            ["-mlong-calls"] = true,
            ["-fcxx-modules"] = true,
            ["-gcodeview"] = true,
            ["-fblocks"] = true,
            ["-mlocal-sdata"] = true,
            ["-emit-llvm"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-nostdinc"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-debug-macro"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-include"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-E"] = true,
            ["-save-temps"] = true,
            ["-gembed-source"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-msave-restore"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-static-openmp"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-ffixed-x14"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-print-effective-triple"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fno-declspec"] = true,
            ["-fopenmp-extensions"] = true,
            ["-rpath"] = true,
            ["-fseh-exceptions"] = true,
            ["-fpascal-strings"] = true,
            ["-verify-pch"] = true,
            ["-mnvj"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-print-multiarch"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-show-source-location"] = true,
            ["-flto"] = true,
            ["-ftime-trace"] = true,
            ["-ffixed-x23"] = true,
            ["-fstack-protector-all"] = true,
            ["-fpch-codegen"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fsystem-module"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fzvector"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-imacros"] = true,
            ["--analyze"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mno-outline"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mrelax"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-memops"] = true,
            ["-fcall-saved-x15"] = true,
            ["-D"] = true,
            ["-ffixed-a2"] = true,
            ["-CC"] = true,
            ["-MM"] = true,
            ["-shared-libsan"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fstrict-enums"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-operator-names"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["--verify-debug-info"] = true,
            ["-mno-neg-immediates"] = true,
            ["-rewrite-objc"] = true,
            ["-emit-ast"] = true,
            ["-mno-save-restore"] = true,
            ["-mno-nvj"] = true,
            ["-cl-opt-disable"] = true,
            ["-MV"] = true,
            ["-mno-long-calls"] = true,
            ["-mmadd4"] = true,
            ["-cl-no-stdinc"] = true,
            ["-mrecord-mcount"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-c"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mnvs"] = true,
            ["-finstrument-functions"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ffixed-x16"] = true,
            ["-fms-extensions"] = true,
            ["-b"] = true,
            ["-finline-hint-functions"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-print-runtime-dir"] = true,
            ["-meabi"] = true,
            ["-fdata-sections"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mcumode"] = true,
            ["-fno-signed-char"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mthread-model"] = true,
            ["-C"] = true,
            ["-fsplit-stack"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-ffreestanding"] = true,
            ["-mpacked-stack"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mno-seses"] = true,
            ["-fno-temp-file"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-plt"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mgpopt"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-mgeneral-regs-only"] = true,
            ["--cuda-host-only"] = true,
            ["-fcoverage-mapping"] = true,
            ["-gdwarf64"] = true,
            ["-mno-implicit-float"] = true,
            ["-mlong-double-64"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fminimize-whitespace"] = true,
            ["-MF"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-static-libsan"] = true,
            ["-ffixed-x12"] = true,
            ["--help-hidden"] = true,
            ["-fno-xray-function-index"] = true,
            ["-ffixed-d6"] = true,
            ["-fropi"] = true,
            ["-gno-embed-source"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fprotect-parens"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-z"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-x2"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-mmemops"] = true,
            ["-fenable-matrix"] = true,
            ["-ffixed-x27"] = true,
            ["-fmodules"] = true,
            ["-fsanitize-stats"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mibt-seal"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fno-trigraphs"] = true,
            ["-dM"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fapplication-extension"] = true,
            ["-fdeclspec"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fdebug-macro"] = true,
            ["-mbackchain"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ffixed-x8"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fno-digraphs"] = true,
            ["--emit-static-lib"] = true,
            ["--no-cuda-version-check"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-MQ"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-mincremental-linker-compatible"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--strip-all"] = true,
            ["--enable-auto-import"] = true,
            ["--dynamicbase"] = true,
            ["-s"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-m"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--no-fatal-warnings"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-demangle"] = true,
            ["--nxcompat"] = true,
            ["--demangle"] = true,
            ["-dn"] = true,
            ["--disable-dynamicbase"] = true,
            ["--verbose"] = true,
            ["--export-all-symbols"] = true,
            ["--allow-multiple-definition"] = true,
            ["-S"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--large-address-aware"] = true,
            ["--strip-debug"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--kill-at"] = true,
            ["-v"] = true,
            ["--no-whole-archive"] = true,
            ["--Bstatic"] = true,
            ["--tsaware"] = true,
            ["--Bdynamic"] = true,
            ["--fatal-warnings"] = true,
            ["--high-entropy-va"] = true,
            ["--insert-timestamp"] = true,
            ["-dy"] = true,
            ["-l"] = true,
            ["--whole-archive"] = true,
            ["--no-seh"] = true,
            ["--version"] = true,
            ["--disable-nxcompat"] = true,
            ["-o"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-no-seh"] = true,
            ["-L"] = true,
            ["--no-dynamicbase"] = true,
            ["--help"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--appcontainer"] = true,
            ["--disable-auto-import"] = true,
            ["--shared"] = true,
            ["--disable-tsaware"] = true,
            ["--gc-sections"] = true,
            ["-static"] = true,
            ["--enable-stdcall-fixup"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    }
}