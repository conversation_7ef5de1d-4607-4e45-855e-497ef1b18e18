{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    find_program = {
        git = [[C:\Program Files\Git\cmd\git.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        nim = false,
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        zig = false,
        clang = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-o"] = true,
            ["--no-insert-timestamp"] = true,
            ["-m"] = true,
            ["--enable-auto-import"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-dy"] = true,
            ["-s"] = true,
            ["--disable-no-seh"] = true,
            ["--whole-archive"] = true,
            ["--no-demangle"] = true,
            ["--fatal-warnings"] = true,
            ["-dn"] = true,
            ["--dynamicbase"] = true,
            ["--appcontainer"] = true,
            ["--large-address-aware"] = true,
            ["--tsaware"] = true,
            ["--no-dynamicbase"] = true,
            ["--strip-debug"] = true,
            ["--disable-nxcompat"] = true,
            ["--disable-tsaware"] = true,
            ["--no-seh"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-fatal-warnings"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--demangle"] = true,
            ["--disable-auto-import"] = true,
            ["--help"] = true,
            ["--verbose"] = true,
            ["--no-gc-sections"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["-L"] = true,
            ["-S"] = true,
            ["-l"] = true,
            ["--disable-high-entropy-va"] = true,
            ["-static"] = true,
            ["--disable-dynamicbase"] = true,
            ["--kill-at"] = true,
            ["-v"] = true,
            ["--high-entropy-va"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--nxcompat"] = true,
            ["--gc-sections"] = true,
            ["--insert-timestamp"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-whole-archive"] = true,
            ["--export-all-symbols"] = true,
            ["--strip-all"] = true,
            ["--Bdynamic"] = true,
            ["--version"] = true,
            ["--exclude-all-symbols"] = true,
            ["--Bstatic"] = true,
            ["--shared"] = true
        }
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            version = "10.2.1",
            links = {
                "fmt"
            },
            static = true,
            license = "MIT"
        },
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3"
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-"
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        ninja = false
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-print-ivar-layout"] = true,
            ["-mrelax-all"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-spell-checking"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["--hip-link"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-trigraphs"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-x15"] = true,
            ["-ffixed-a6"] = true,
            ["-ffixed-x28"] = true,
            ["-msoft-float"] = true,
            ["-ffixed-x21"] = true,
            ["-dM"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-ffixed-x29"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-discard-value-names"] = true,
            ["-extract-api"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-cxx-isystem"] = true,
            ["-mno-extern-sdata"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-ffixed-x2"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-Xassembler"] = true,
            ["-mno-movt"] = true,
            ["-mno-code-object-v3"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mno-msa"] = true,
            ["-finline-hint-functions"] = true,
            ["-ffixed-x23"] = true,
            ["-fgpu-rdc"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-Wdeprecated"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fsystem-module"] = true,
            ["-imacros"] = true,
            ["-fmerge-all-constants"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-mnvj"] = true,
            ["-mlong-calls"] = true,
            ["-emit-ast"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-z"] = true,
            ["-fxray-instrument"] = true,
            ["-ffixed-a2"] = true,
            ["-ffixed-d5"] = true,
            ["-isystem-after"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fno-elide-constructors"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["--cuda-host-only"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-use-init-array"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fmodules-search-all"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fprofile-generate"] = true,
            ["-fsycl"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-U"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-memory-profile"] = true,
            ["-fcommon"] = true,
            ["-fpch-debuginfo"] = true,
            ["-time"] = true,
            ["-mno-restrict-it"] = true,
            ["-ffixed-d4"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-signed-char"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-rtti-data"] = true,
            ["-fdeclspec"] = true,
            ["-munaligned-access"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-mno-nvs"] = true,
            ["-ffixed-x12"] = true,
            ["-fshort-enums"] = true,
            ["-gcodeview"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mskip-rax-setup"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-moutline"] = true,
            ["-fno-access-control"] = true,
            ["-fsave-optimization-record"] = true,
            ["-mlong-double-128"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-dependency-file"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-MMD"] = true,
            ["-MD"] = true,
            ["-fcs-profile-generate"] = true,
            ["-flto"] = true,
            ["-dI"] = true,
            ["-fno-plt"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fasync-exceptions"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-mno-implicit-float"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-show-column"] = true,
            ["-ffunction-sections"] = true,
            ["-fms-compatibility"] = true,
            ["-fno-stack-protector"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fno-new-infallible"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-ffixed-x1"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-ffixed-x22"] = true,
            ["-ftime-trace"] = true,
            ["-fdebug-macro"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-Xpreprocessor"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-mno-outline"] = true,
            ["-ffixed-a5"] = true,
            ["-ffixed-x4"] = true,
            ["-save-temps"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-gdwarf-4"] = true,
            ["-mgpopt"] = true,
            ["-freciprocal-math"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fmodules"] = true,
            ["-mabicalls"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fstack-size-section"] = true,
            ["-fconvergent-functions"] = true,
            ["-I-"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fgnu-runtime"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-mno-local-sdata"] = true,
            ["-fstrict-enums"] = true,
            ["-ffixed-x5"] = true,
            ["-MT"] = true,
            ["-MP"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-mt"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fms-hotpatch"] = true,
            ["-fblocks"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-T"] = true,
            ["-fstack-protector-all"] = true,
            ["-mmemops"] = true,
            ["-fopenmp-simd"] = true,
            ["-iquote"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-M"] = true,
            ["-ffreestanding"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fcall-saved-x10"] = true,
            ["-x"] = true,
            ["--config"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffixed-x13"] = true,
            ["-fsanitize-stats"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-ffixed-d0"] = true,
            ["-gdwarf64"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mseses"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fcxx-exceptions"] = true,
            ["-pg"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-membedded-data"] = true,
            ["-save-stats"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-w"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fmodules-decluse"] = true,
            ["-ffixed-x25"] = true,
            ["-meabi"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fborland-extensions"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fno-digraphs"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-print-effective-triple"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mno-madd4"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mllvm"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-gdwarf-3"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-P"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-strict-return"] = true,
            ["-mhvx"] = true,
            ["-ffixed-d2"] = true,
            ["-mno-crc"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-ffixed-x10"] = true,
            ["-finline-functions"] = true,
            ["--analyzer-output"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mno-memops"] = true,
            ["-ffixed-x24"] = true,
            ["--analyze"] = true,
            ["-fobjc-arc"] = true,
            ["-fno-fixed-point"] = true,
            ["-fmodules-ts"] = true,
            ["-mno-global-merge"] = true,
            ["-I"] = true,
            ["-trigraphs"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-arch"] = true,
            ["-nohipwrapperinc"] = true,
            ["-mfp64"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fwasm-exceptions"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-frwpi"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fno-rtti"] = true,
            ["-ffixed-d6"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-profile-generate"] = true,
            ["-fapple-kext"] = true,
            ["-iprefix"] = true,
            ["-mlong-double-64"] = true,
            ["-nostdinc"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-mmark-bti-property"] = true,
            ["-emit-interface-stubs"] = true,
            ["-ffixed-d1"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-iwithsysroot"] = true,
            ["-fno-show-source-location"] = true,
            ["-fcxx-modules"] = true,
            ["-help"] = true,
            ["-fno-operator-names"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["--verify-debug-info"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-mno-seses"] = true,
            ["--help-hidden"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fopenmp"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-nobuiltininc"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fstandalone-debug"] = true,
            ["-mno-nvj"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fmemory-profile"] = true,
            ["-print-multiarch"] = true,
            ["-L"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-ffixed-x3"] = true,
            ["-print-resource-dir"] = true,
            ["-mextern-sdata"] = true,
            ["-mcmse"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fno-integrated-as"] = true,
            ["-mrelax"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-lto"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-ffixed-a1"] = true,
            ["-fgpu-sanitize"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-declspec"] = true,
            ["-faligned-allocation"] = true,
            ["-mstackrealign"] = true,
            ["-fdigraphs"] = true,
            ["-mexecute-only"] = true,
            ["-mfp32"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mlvi-cfi"] = true,
            ["-mmt"] = true,
            ["-ffixed-x31"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mcumode"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-funroll-loops"] = true,
            ["-gdwarf-2"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-static-openmp"] = true,
            ["-E"] = true,
            ["--version"] = true,
            ["-gno-embed-source"] = true,
            ["-mms-bitfields"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mno-gpopt"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fapprox-func"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fsigned-char"] = true,
            ["-fsized-deallocation"] = true,
            ["-index-header-map"] = true,
            ["-gdwarf-5"] = true,
            ["-fprotect-parens"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fms-extensions"] = true,
            ["-fstack-protector-strong"] = true,
            ["-print-supported-cpus"] = true,
            ["-freroll-loops"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-ffixed-r19"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fintegrated-as"] = true,
            ["-ffixed-x14"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-pthread"] = true,
            ["-CC"] = true,
            ["-F"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-nogpulib"] = true,
            ["-relocatable-pch"] = true,
            ["-mno-long-calls"] = true,
            ["-emit-merged-ifs"] = true,
            ["-print-search-dirs"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fexceptions"] = true,
            ["-fno-builtin"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-mno-packets"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fcf-protection"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fnew-infallible"] = true,
            ["-mlocal-sdata"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-Xlinker"] = true,
            ["-include-pch"] = true,
            ["--cuda-device-only"] = true,
            ["-fforce-enable-int128"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-G"] = true,
            ["-fno-signed-zeros"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fno-pch-codegen"] = true,
            ["-mcrc"] = true,
            ["-Ttext"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mibt-seal"] = true,
            ["-print-runtime-dir"] = true,
            ["-ffixed-x8"] = true,
            ["-mno-outline-atomics"] = true,
            ["-cl-mad-enable"] = true,
            ["-fxray-link-deps"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-emit-module"] = true,
            ["-ffixed-a3"] = true,
            ["-fembed-bitcode"] = true,
            ["--gpu-bundle-output"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-working-directory"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fslp-vectorize"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fno-short-wchar"] = true,
            ["-print-targets"] = true,
            ["-dsym-dir"] = true,
            ["-fminimize-whitespace"] = true,
            ["-H"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-mnvs"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-malign-double"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-o"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fmath-errno"] = true,
            ["-mlong-double-80"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-miamcu"] = true,
            ["-mmadd4"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fobjc-weak"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-Xopenmp-target"] = true,
            ["-undef"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-ffixed-x18"] = true,
            ["-mglobal-merge"] = true,
            ["-ffixed-x30"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-isysroot"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-save-restore"] = true,
            ["-fstack-protector"] = true,
            ["-fcall-saved-x12"] = true,
            ["-b"] = true,
            ["-nogpuinc"] = true,
            ["-MF"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["--precompile"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-module-file-info"] = true,
            ["-dependency-dot"] = true,
            ["-mno-tgsplit"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-C"] = true,
            ["-ffinite-loops"] = true,
            ["-ffixed-a4"] = true,
            ["-mhvx-qfloat"] = true,
            ["-include"] = true,
            ["-fno-split-stack"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-mno-cumode"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-gline-directives-only"] = true,
            ["-gdwarf"] = true,
            ["-emit-llvm"] = true,
            ["-fno-sycl"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fshort-wchar"] = true,
            ["-mno-embedded-data"] = true,
            ["-ffixed-x19"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-Qunused-arguments"] = true,
            ["-mrecord-mcount"] = true,
            ["-fropi"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-c"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-msave-restore"] = true,
            ["-foffload-lto"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-B"] = true,
            ["-Tbss"] = true,
            ["-gmodules"] = true,
            ["-rpath"] = true,
            ["-fdata-sections"] = true,
            ["-femulated-tls"] = true,
            ["-freg-struct-return"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-x20"] = true,
            ["-fenable-matrix"] = true,
            ["-Qy"] = true,
            ["-fvectorize"] = true,
            ["-MQ"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mthread-model"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffixed-x6"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-pedantic"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fstack-usage"] = true,
            ["-fno-cxx-modules"] = true,
            ["-mqdsp6-compat"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-maix-struct-return"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mno-relax"] = true,
            ["-mno-execute-only"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-autolink"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fpch-codegen"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mlvi-hardening"] = true,
            ["-mmsa"] = true,
            ["-fcall-saved-x9"] = true,
            ["-static-libsan"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-isystem"] = true,
            ["-fno-offload-lto"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fdebug-types-section"] = true,
            ["-Qn"] = true,
            ["-MG"] = true,
            ["-fzvector"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-exceptions"] = true,
            ["-Xclang"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-moutline-atomics"] = true,
            ["-v"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-iwithprefix"] = true,
            ["-fintegrated-cc1"] = true,
            ["-ffixed-r9"] = true,
            ["-dD"] = true,
            ["-D"] = true,
            ["-traditional-cpp"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mbackchain"] = true,
            ["-S"] = true,
            ["-mno-hvx"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-ftrapv"] = true,
            ["-mrestrict-it"] = true,
            ["-fcall-saved-x14"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-Tdata"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-cl-opt-disable"] = true,
            ["-ffixed-x27"] = true,
            ["-ffast-math"] = true,
            ["-mpacked-stack"] = true,
            ["-verify-pch"] = true,
            ["-fno-debug-macro"] = true,
            ["-fno-global-isel"] = true,
            ["-ffixed-x7"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fuse-line-directives"] = true,
            ["-fwritable-strings"] = true,
            ["-g"] = true,
            ["-MJ"] = true,
            ["-mrtd"] = true,
            ["-mfentry"] = true,
            ["-ffixed-x11"] = true,
            ["-MV"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fignore-exceptions"] = true,
            ["-fapplication-extension"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-elide-type"] = true,
            ["-femit-all-decls"] = true,
            ["-mnocrc"] = true,
            ["-gembed-source"] = true,
            ["-shared-libsan"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-gdwarf32"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-MM"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-module-dependency-dir"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-unaligned-access"] = true,
            ["-idirafter"] = true,
            ["-print-target-triple"] = true,
            ["-ffixed-d3"] = true,
            ["-fjump-tables"] = true,
            ["-ffixed-x26"] = true,
            ["-mstack-arg-probe"] = true,
            ["-pipe"] = true,
            ["-fno-temp-file"] = true,
            ["-msvr4-struct-return"] = true,
            ["-ffixed-x16"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-mpackets"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-gline-tables-only"] = true,
            ["-ffixed-point"] = true,
            ["-fseh-exceptions"] = true,
            ["-fpascal-strings"] = true,
            ["-fgnu89-inline"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-ffixed-x9"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-Xanalyzer"] = true,
            ["-fno-common"] = true,
            ["-faddrsig"] = true,
            ["-ibuiltininc"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-a0"] = true,
            ["-mtgsplit"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}