{
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_program = {
        gcc = false,
        clang = false,
        gzip = [[C:\Program Files\Git\usr\bin\gzip.exe]],
        git = [[C:\Program Files\Git\mingw64\bin\git.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        tar = [[C:\Program Files\Git\usr\bin\tar.exe]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--large-address-aware"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-s"] = true,
            ["--allow-multiple-definition"] = true,
            ["--export-all-symbols"] = true,
            ["-static"] = true,
            ["--verbose"] = true,
            ["-S"] = true,
            ["--tsaware"] = true,
            ["--strip-debug"] = true,
            ["-L"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--Bdynamic"] = true,
            ["--disable-auto-import"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--demangle"] = true,
            ["-m"] = true,
            ["--exclude-all-symbols"] = true,
            ["-o"] = true,
            ["--strip-all"] = true,
            ["--no-demangle"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--disable-no-seh"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--nxcompat"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-tsaware"] = true,
            ["--disable-dynamicbase"] = true,
            ["--no-dynamicbase"] = true,
            ["--no-fatal-warnings"] = true,
            ["-v"] = true,
            ["--shared"] = true,
            ["--help"] = true,
            ["--no-insert-timestamp"] = true,
            ["--version"] = true,
            ["--whole-archive"] = true,
            ["--appcontainer"] = true,
            ["--no-seh"] = true,
            ["--dynamicbase"] = true,
            ["--Bstatic"] = true,
            ["--gc-sections"] = true,
            ["-dn"] = true,
            ["--no-whole-archive"] = true,
            ["--kill-at"] = true,
            ["--no-gc-sections"] = true,
            ["-l"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--enable-auto-import"] = true,
            ["-dy"] = true,
            ["--fatal-warnings"] = true,
            ["--disable-nxcompat"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fdebug-macro"] = true,
            ["-working-directory"] = true,
            ["-ffixed-r9"] = true,
            ["-fnew-infallible"] = true,
            ["-mpacked-stack"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mrelax-all"] = true,
            ["-dD"] = true,
            ["-ffixed-x28"] = true,
            ["-moutline-atomics"] = true,
            ["-mno-nvs"] = true,
            ["-fno-unroll-loops"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-H"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-short-wchar"] = true,
            ["-fcxx-exceptions"] = true,
            ["-dM"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fzvector"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-mno-movt"] = true,
            ["-mno-embedded-data"] = true,
            ["-fsycl"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-ffixed-d0"] = true,
            ["-foffload-lto"] = true,
            ["--cuda-host-only"] = true,
            ["-gcodeview"] = true,
            ["-include"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-CC"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mseses"] = true,
            ["-mrecord-mcount"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-gdwarf"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mno-memops"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-ffixed-r19"] = true,
            ["-fintegrated-as"] = true,
            ["-ibuiltininc"] = true,
            ["-fmodules-decluse"] = true,
            ["-G"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-ffixed-d6"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fno-temp-file"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mnop-mcount"] = true,
            ["-ffixed-d2"] = true,
            ["-fgnu-keywords"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-iquote"] = true,
            ["-ffixed-x31"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fcall-saved-x14"] = true,
            ["-meabi"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-L"] = true,
            ["-mnvs"] = true,
            ["-msave-restore"] = true,
            ["-nohipwrapperinc"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-emit-llvm"] = true,
            ["-fdeclspec"] = true,
            ["-mcumode"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fuse-line-directives"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-ffunction-sections"] = true,
            ["-ffinite-loops"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fvectorize"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-stack-protector"] = true,
            ["-finline-hint-functions"] = true,
            ["-fapprox-func"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-integrated-as"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-shared-libsan"] = true,
            ["-dI"] = true,
            ["-fembed-bitcode"] = true,
            ["-ffast-math"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-trigraphs"] = true,
            ["-fms-extensions"] = true,
            ["-ffixed-d1"] = true,
            ["-fconvergent-functions"] = true,
            ["-arch"] = true,
            ["-fcall-saved-x10"] = true,
            ["--version"] = true,
            ["-ffixed-x19"] = true,
            ["-ffixed-x4"] = true,
            ["-mabicalls"] = true,
            ["-Xlinker"] = true,
            ["-fopenmp-simd"] = true,
            ["-fsized-deallocation"] = true,
            ["-include-pch"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mno-local-sdata"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fpch-codegen"] = true,
            ["-fmodules-search-all"] = true,
            ["-fstack-usage"] = true,
            ["-fxray-instrument"] = true,
            ["-iwithsysroot"] = true,
            ["-mcmse"] = true,
            ["-mno-packets"] = true,
            ["-MD"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-ffixed-x27"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-Ttext"] = true,
            ["-fno-lto"] = true,
            ["-mfentry"] = true,
            ["-imacros"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fstack-size-section"] = true,
            ["-ftrapv"] = true,
            ["-fno-xray-function-index"] = true,
            ["-MQ"] = true,
            ["-mlong-double-128"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-gline-directives-only"] = true,
            ["-ffixed-x23"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-mibt-seal"] = true,
            ["-extract-api"] = true,
            ["-Xclang"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-emit-module"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-print-resource-dir"] = true,
            ["-Tbss"] = true,
            ["-Qy"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-z"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-rpath"] = true,
            ["-MJ"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fno-digraphs"] = true,
            ["-mno-outline"] = true,
            ["-mno-implicit-float"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-Tdata"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fjump-tables"] = true,
            ["-idirafter"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-Xanalyzer"] = true,
            ["-fobjc-arc"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-ffixed-a1"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-declspec"] = true,
            ["-ffixed-x5"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffreestanding"] = true,
            ["-miamcu"] = true,
            ["-fstack-protector-all"] = true,
            ["-fcxx-modules"] = true,
            ["-malign-double"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fprotect-parens"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["--emit-static-lib"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-finline-functions"] = true,
            ["-ffixed-x21"] = true,
            ["-isysroot"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-MG"] = true,
            ["-ffixed-x15"] = true,
            ["-MV"] = true,
            ["-c"] = true,
            ["-fslp-vectorize"] = true,
            ["-fno-profile-generate"] = true,
            ["-trigraphs"] = true,
            ["-fcall-saved-x15"] = true,
            ["-faligned-allocation"] = true,
            ["-moutline"] = true,
            ["-nostdinc"] = true,
            ["-B"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-x16"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-use-init-array"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-pg"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mlvi-hardening"] = true,
            ["-isystem-after"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mmt"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-ffixed-x20"] = true,
            ["-C"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fmath-errno"] = true,
            ["-mno-crc"] = true,
            ["-mno-cumode"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-common"] = true,
            ["-w"] = true,
            ["-index-header-map"] = true,
            ["-gdwarf32"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcoverage-mapping"] = true,
            ["-time"] = true,
            ["-femit-all-decls"] = true,
            ["-fasync-exceptions"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-rtti"] = true,
            ["-mcrc"] = true,
            ["-fsanitize-trap"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-signed-zeros"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fxl-pragma-pack"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mllvm"] = true,
            ["-fno-sycl"] = true,
            ["-freg-struct-return"] = true,
            ["-gdwarf-5"] = true,
            ["-nogpuinc"] = true,
            ["-mstackrealign"] = true,
            ["-mno-save-restore"] = true,
            ["-relocatable-pch"] = true,
            ["-mno-seses"] = true,
            ["-mnvj"] = true,
            ["-print-target-triple"] = true,
            ["-fno-plt"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-static-libsan"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fsystem-module"] = true,
            ["-fcall-saved-x12"] = true,
            ["--no-cuda-version-check"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ffixed-d3"] = true,
            ["-pthread"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fforce-enable-int128"] = true,
            ["-gdwarf64"] = true,
            ["-mno-msa"] = true,
            ["-fms-compatibility"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-MP"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-funroll-loops"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fdata-sections"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["--precompile"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fms-hotpatch"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fgnu89-inline"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-D"] = true,
            ["-g"] = true,
            ["-fno-spell-checking"] = true,
            ["-MMD"] = true,
            ["-ffixed-x24"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-ffixed-a5"] = true,
            ["-fsave-optimization-record"] = true,
            ["-mno-long-calls"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fgnu-runtime"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fblocks"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mms-bitfields"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x8"] = true,
            ["-fseh-exceptions"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fmemory-profile"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fexceptions"] = true,
            ["-fno-operator-names"] = true,
            ["-gcodeview-ghash"] = true,
            ["-print-runtime-dir"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fgpu-rdc"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-U"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-split-stack"] = true,
            ["-Xpreprocessor"] = true,
            ["-ivfsoverlay"] = true,
            ["-mno-madd4"] = true,
            ["-ffixed-x9"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcall-saved-x11"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-MT"] = true,
            ["-mstack-arg-probe"] = true,
            ["-ffixed-x25"] = true,
            ["-ffixed-a6"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-mno-tgsplit"] = true,
            ["-fsplit-stack"] = true,
            ["-gno-embed-source"] = true,
            ["-v"] = true,
            ["-cxx-isystem"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mlocal-sdata"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-standalone-debug"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-S"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mhvx"] = true,
            ["-fkeep-static-consts"] = true,
            ["-ffixed-point"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-mgpopt"] = true,
            ["-frwpi"] = true,
            ["-fno-jump-tables"] = true,
            ["-save-stats"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-MF"] = true,
            ["-save-temps"] = true,
            ["-fopenmp"] = true,
            ["-fcf-protection"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["--analyze"] = true,
            ["-fshort-enums"] = true,
            ["-fstack-protector"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-debug-macro"] = true,
            ["-nobuiltininc"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fenable-matrix"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-mhvx-qfloat"] = true,
            ["-o"] = true,
            ["-mno-neg-immediates"] = true,
            ["-mlong-calls"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fmodules"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-MM"] = true,
            ["-msoft-float"] = true,
            ["-P"] = true,
            ["--config"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fsigned-char"] = true,
            ["-fwritable-strings"] = true,
            ["-rewrite-objc"] = true,
            ["-ffixed-d7"] = true,
            ["-fpascal-strings"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-ffixed-x13"] = true,
            ["-ffixed-x22"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mno-hvx"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-nogpulib"] = true,
            ["-fdebug-types-section"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-Wdeprecated"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fno-offload-lto"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-global-isel"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mmark-bti-property"] = true,
            ["-mthread-model"] = true,
            ["-femulated-tls"] = true,
            ["-fborland-extensions"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mmemops"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-membedded-data"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-ffixed-x3"] = true,
            ["-mlong-double-64"] = true,
            ["-ftrigraphs"] = true,
            ["-b"] = true,
            ["-flto"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["--hip-link"] = true,
            ["-mqdsp6-compat"] = true,
            ["-F"] = true,
            ["-I-"] = true,
            ["-mno-nvj"] = true,
            ["-mno-extern-sdata"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-autolink"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-access-control"] = true,
            ["-module-dependency-dir"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-mmsa"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffixed-x11"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-ffixed-d5"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-rtti-data"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-ftime-trace"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-show-source-location"] = true,
            ["-fxray-link-deps"] = true,
            ["-traditional-cpp"] = true,
            ["-faddrsig"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fsanitize-stats"] = true,
            ["-help"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mbackchain"] = true,
            ["-mmadd4"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fapple-kext"] = true,
            ["-fintegrated-cc1"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fno-new-infallible"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-builtin"] = true,
            ["-iprefix"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-ffixed-x7"] = true,
            ["-iwithprefixbefore"] = true,
            ["-pedantic"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-M"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-print-targets"] = true,
            ["-mexecute-only"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-gembed-source"] = true,
            ["-mlvi-cfi"] = true,
            ["-fno-exceptions"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-gmodules"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fno-discard-value-names"] = true,
            ["-isystem"] = true,
            ["-Qn"] = true,
            ["-x"] = true,
            ["--analyzer-output"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-mfp32"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mfp64"] = true,
            ["-gdwarf-3"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fno-strict-return"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-print-supported-cpus"] = true,
            ["-ffixed-a4"] = true,
            ["-fansi-escape-codes"] = true,
            ["-mno-mt"] = true,
            ["-fcall-saved-x9"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-T"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-Xassembler"] = true,
            ["-ffixed-d4"] = true,
            ["-verify-pch"] = true,
            ["-mnocrc"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-ffixed-x6"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-munaligned-access"] = true,
            ["--help-hidden"] = true,
            ["-mrelax"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-mtgsplit"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-ffixed-a0"] = true,
            ["-fapplication-extension"] = true,
            ["-mcode-object-v3"] = true,
            ["-ffixed-x2"] = true,
            ["--gpu-bundle-output"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-show-column"] = true,
            ["-mno-global-merge"] = true,
            ["-mglobal-merge"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fcommon"] = true,
            ["-fdigraphs"] = true,
            ["-ffixed-x17"] = true,
            ["-fcoroutines-ts"] = true,
            ["-gline-tables-only"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["--migrate"] = true,
            ["-fstrict-enums"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fno-pch-codegen"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fropi"] = true,
            ["-cl-mad-enable"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-ffixed-x29"] = true,
            ["-E"] = true,
            ["-fno-fixed-point"] = true,
            ["-dependency-file"] = true,
            ["-undef"] = true,
            ["-iwithprefix"] = true,
            ["-I"] = true,
            ["-ffixed-x30"] = true,
            ["-fcall-saved-x13"] = true,
            ["-module-file-info"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-a3"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mno-relax"] = true,
            ["-print-multiarch"] = true,
            ["-emit-interface-stubs"] = true,
            ["-pipe"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-ffixed-x12"] = true,
            ["-print-effective-triple"] = true,
            ["-fignore-exceptions"] = true,
            ["-mrtd"] = true,
            ["-freciprocal-math"] = true,
            ["-fshort-wchar"] = true,
            ["-mpackets"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-dsym-dir"] = true,
            ["-mno-restrict-it"] = true,
            ["-cl-finite-math-only"] = true,
            ["-static-openmp"] = true,
            ["-fverbose-asm"] = true,
            ["-freroll-loops"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-cl-opt-disable"] = true,
            ["-emit-ast"] = true,
            ["-ffixed-x10"] = true,
            ["-dependency-dot"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-x1"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fno-signed-char"] = true,
            ["-munsafe-fp-atomics"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            license = "MIT",
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            links = {
                "fmt"
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            version = "10.2.1",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            static = true,
            license = "MIT",
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            }
        }
    }
}