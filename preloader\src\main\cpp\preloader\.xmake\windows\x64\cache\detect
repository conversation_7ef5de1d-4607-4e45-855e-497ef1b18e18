{
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-ffixed-point"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-include-pch"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-lto"] = true,
            ["-module-file-info"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-femulated-tls"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fvectorize"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-dM"] = true,
            ["-print-target-triple"] = true,
            ["-malign-double"] = true,
            ["-mlong-double-128"] = true,
            ["-ffixed-d7"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mlocal-sdata"] = true,
            ["-mskip-rax-setup"] = true,
            ["-T"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fcall-saved-x18"] = true,
            ["-v"] = true,
            ["-pthread"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fstandalone-debug"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-mno-tgsplit"] = true,
            ["-cxx-isystem"] = true,
            ["-time"] = true,
            ["-mno-nvj"] = true,
            ["-ffixed-x18"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fintegrated-as"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["--emit-static-lib"] = true,
            ["-frwpi"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fasync-exceptions"] = true,
            ["-mno-abicalls"] = true,
            ["-fmath-errno"] = true,
            ["-freroll-loops"] = true,
            ["-fcoroutines-ts"] = true,
            ["-Tdata"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-rewrite-objc"] = true,
            ["-mno-gpopt"] = true,
            ["-fno-trigraphs"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-mextern-sdata"] = true,
            ["-mcrc"] = true,
            ["-fno-exceptions"] = true,
            ["-fxray-instrument"] = true,
            ["-mnvj"] = true,
            ["-ffixed-x23"] = true,
            ["-gcodeview"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-save-temps"] = true,
            ["-P"] = true,
            ["-mno-mt"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fgnu-runtime"] = true,
            ["-faddrsig"] = true,
            ["-fno-show-source-location"] = true,
            ["-fexceptions"] = true,
            ["-mmt"] = true,
            ["--analyzer-output"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fmodules-search-all"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fgpu-rdc"] = true,
            ["-msave-restore"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fobjc-arc"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-xray-function-index"] = true,
            ["-I-"] = true,
            ["-fstack-size-section"] = true,
            ["-cl-opt-disable"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-mno-local-sdata"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["--version"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fsigned-char"] = true,
            ["-mcmse"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fms-hotpatch"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fnew-infallible"] = true,
            ["-print-multiarch"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-CC"] = true,
            ["-msoft-float"] = true,
            ["-pipe"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-MD"] = true,
            ["-mno-movt"] = true,
            ["-fkeep-static-consts"] = true,
            ["-mno-embedded-data"] = true,
            ["-ffixed-x8"] = true,
            ["-S"] = true,
            ["-iprefix"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fsanitize-stats"] = true,
            ["-fpcc-struct-return"] = true,
            ["-undef"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-cl-mad-enable"] = true,
            ["-o"] = true,
            ["-finstrument-functions"] = true,
            ["-ffixed-x20"] = true,
            ["-L"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-signed-zeros"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-elide-constructors"] = true,
            ["-mstackrealign"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fapple-kext"] = true,
            ["-Wdeprecated"] = true,
            ["-mmadd4"] = true,
            ["-mno-execute-only"] = true,
            ["-static-libsan"] = true,
            ["-fdebug-macro"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-print-ivar-layout"] = true,
            ["-M"] = true,
            ["-fmemory-profile"] = true,
            ["-ffixed-x13"] = true,
            ["-ffixed-r19"] = true,
            ["-finline-functions"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fcall-saved-x9"] = true,
            ["-imacros"] = true,
            ["-pg"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-shared-libsan"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-save-stats"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-print-runtime-dir"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fgpu-sanitize"] = true,
            ["--migrate"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-d6"] = true,
            ["-ftrapv"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fsplit-stack"] = true,
            ["-fsanitize-trap"] = true,
            ["-iquote"] = true,
            ["-iwithprefix"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-freg-struct-return"] = true,
            ["-gcodeview-ghash"] = true,
            ["-ibuiltininc"] = true,
            ["-pedantic"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-faligned-allocation"] = true,
            ["-ffixed-d5"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-pch-codegen"] = true,
            ["-ffixed-x30"] = true,
            ["-ftrigraphs"] = true,
            ["-mstack-arg-probe"] = true,
            ["-Xlinker"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-I"] = true,
            ["-arch"] = true,
            ["-fwritable-strings"] = true,
            ["-fdigraphs"] = true,
            ["-Xassembler"] = true,
            ["-fopenmp"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-nogpulib"] = true,
            ["-emit-ast"] = true,
            ["-fms-extensions"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-mno-hvx"] = true,
            ["-fborland-extensions"] = true,
            ["-mno-unaligned-access"] = true,
            ["-finline-hint-functions"] = true,
            ["--cuda-host-only"] = true,
            ["-nohipwrapperinc"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-serialize-diagnostics"] = true,
            ["-moutline-atomics"] = true,
            ["-fms-compatibility"] = true,
            ["-mgpopt"] = true,
            ["-E"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-digraphs"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fconvergent-functions"] = true,
            ["-ffixed-x4"] = true,
            ["-ffixed-a2"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mlong-calls"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-plt"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fpascal-strings"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fcoverage-mapping"] = true,
            ["-D"] = true,
            ["-cl-finite-math-only"] = true,
            ["-nostdinc"] = true,
            ["-fno-declspec"] = true,
            ["-fverbose-asm"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mglobal-merge"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-emit-module"] = true,
            ["-mno-code-object-v3"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-include"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fno-rtti-data"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-global-isel"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-mlong-double-64"] = true,
            ["-G"] = true,
            ["-dsym-dir"] = true,
            ["-fstrict-enums"] = true,
            ["-fxray-link-deps"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-freciprocal-math"] = true,
            ["-fgnu89-inline"] = true,
            ["-gdwarf-5"] = true,
            ["-fsycl"] = true,
            ["-ffixed-x27"] = true,
            ["-print-supported-cpus"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-ffixed-x3"] = true,
            ["-mfp32"] = true,
            ["-ffixed-a0"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["--end-no-unused-arguments"] = true,
            ["--precompile"] = true,
            ["-membedded-data"] = true,
            ["-F"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-gdwarf-3"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-trigraphs"] = true,
            ["-Xpreprocessor"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-gembed-source"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-access-control"] = true,
            ["-ffixed-x7"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-ffixed-x15"] = true,
            ["-dependency-file"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mfp64"] = true,
            ["-mrecord-mcount"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fignore-exceptions"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mmsa"] = true,
            ["-fshort-enums"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mno-memops"] = true,
            ["-MP"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-offload-lto"] = true,
            ["-working-directory"] = true,
            ["-gdwarf"] = true,
            ["-static-openmp"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-c"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-module-dependency-dir"] = true,
            ["-index-header-map"] = true,
            ["-Xopenmp-target"] = true,
            ["-help"] = true,
            ["-MG"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-operator-names"] = true,
            ["-fmodules-decluse"] = true,
            ["-munaligned-access"] = true,
            ["-C"] = true,
            ["-mnvs"] = true,
            ["-gdwarf32"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-mllvm"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fcall-saved-x11"] = true,
            ["-dI"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fropi"] = true,
            ["-fstack-usage"] = true,
            ["-mrelax"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-mabicalls"] = true,
            ["-emit-llvm"] = true,
            ["-iwithsysroot"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fprotect-parens"] = true,
            ["-b"] = true,
            ["-ffixed-d3"] = true,
            ["-mhvx"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-mrestrict-it"] = true,
            ["-meabi"] = true,
            ["-fopenmp-simd"] = true,
            ["-g"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-ffixed-x6"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-print-targets"] = true,
            ["-mnocrc"] = true,
            ["-extract-api"] = true,
            ["-Qy"] = true,
            ["-MF"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fdata-sections"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-x26"] = true,
            ["-mno-outline-atomics"] = true,
            ["-flto"] = true,
            ["-mno-relax"] = true,
            ["-mpacked-stack"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-short-wchar"] = true,
            ["-Qunused-arguments"] = true,
            ["-fmerge-all-constants"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fstack-protector"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-miamcu"] = true,
            ["-fenable-matrix"] = true,
            ["-fpch-codegen"] = true,
            ["-mno-long-calls"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffixed-x22"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-unique-section-names"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-mcumode"] = true,
            ["-mmark-bti-property"] = true,
            ["-foffload-lto"] = true,
            ["-Xclang"] = true,
            ["-gdwarf-2"] = true,
            ["-MT"] = true,
            ["-MQ"] = true,
            ["-ffreestanding"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-ffixed-d1"] = true,
            ["-mmemops"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mno-crc"] = true,
            ["-fno-rtti"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-finite-loops"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-B"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-mpackets"] = true,
            ["-Xanalyzer"] = true,
            ["-ffixed-x10"] = true,
            ["-funroll-loops"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fno-sycl"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fminimize-whitespace"] = true,
            ["-MV"] = true,
            ["-ffixed-x5"] = true,
            ["-ftime-trace"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-memory-profile"] = true,
            ["-mlvi-cfi"] = true,
            ["-fblocks"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fjump-tables"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fstack-protector-strong"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-elide-type"] = true,
            ["-fdeclspec"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mfentry"] = true,
            ["-print-effective-triple"] = true,
            ["-ffast-math"] = true,
            ["-fno-autolink"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-use-init-array"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-new-infallible"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-isystem"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-gmodules"] = true,
            ["-mibt-seal"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-mms-bitfields"] = true,
            ["-print-resource-dir"] = true,
            ["-ffixed-d4"] = true,
            ["-mno-packets"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-gdwarf-4"] = true,
            ["--verify-debug-info"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-MMD"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fzvector"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-idirafter"] = true,
            ["-mwavefrontsize64"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mrelax-all"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mno-madd4"] = true,
            ["-fansi-escape-codes"] = true,
            ["-ffixed-x12"] = true,
            ["-fuse-line-directives"] = true,
            ["-relocatable-pch"] = true,
            ["-fcommon"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fcxx-modules"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-global-merge"] = true,
            ["-verify-pch"] = true,
            ["-mno-msa"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-Ttext"] = true,
            ["-fmodules"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-gline-directives-only"] = true,
            ["-fno-addrsig"] = true,
            ["-ffixed-d2"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-ffixed-x19"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-gdwarf64"] = true,
            ["-mseses"] = true,
            ["-ffixed-a4"] = true,
            ["-isysroot"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--no-cuda-version-check"] = true,
            ["-mno-implicit-float"] = true,
            ["-fno-unroll-loops"] = true,
            ["-ffixed-x29"] = true,
            ["-fshort-wchar"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-strict-return"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fseh-exceptions"] = true,
            ["-femit-all-decls"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-sanitize-trap"] = true,
            ["--analyze"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-d0"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fcf-protection"] = true,
            ["-Qn"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-rpath"] = true,
            ["-traditional-cpp"] = true,
            ["-ffunction-sections"] = true,
            ["-ffixed-x24"] = true,
            ["-fsized-deallocation"] = true,
            ["-mno-nvs"] = true,
            ["-z"] = true,
            ["-mnop-mcount"] = true,
            ["-fstack-protector-all"] = true,
            ["-fslp-vectorize"] = true,
            ["-ffixed-x17"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-ffinite-loops"] = true,
            ["-w"] = true,
            ["-mno-seses"] = true,
            ["-fno-signed-char"] = true,
            ["-mno-cumode"] = true,
            ["-ffixed-a6"] = true,
            ["-mno-outline"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fwasm-exceptions"] = true,
            ["-isystem-after"] = true,
            ["-nogpuinc"] = true,
            ["-moutline"] = true,
            ["-mno-extern-sdata"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mno-neg-immediates"] = true,
            ["--config"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-dD"] = true,
            ["-ffixed-x21"] = true,
            ["-fembed-bitcode"] = true,
            ["-mno-restrict-it"] = true,
            ["-fno-stack-protector"] = true,
            ["--hip-link"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mexecute-only"] = true,
            ["-mtgsplit"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-debug-macro"] = true,
            ["-U"] = true,
            ["-ffixed-x2"] = true,
            ["--help-hidden"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mrtd"] = true,
            ["-mlvi-hardening"] = true,
            ["-dependency-dot"] = true,
            ["-mthread-model"] = true,
            ["-H"] = true,
            ["-fno-builtin"] = true,
            ["-mbackchain"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fapprox-func"] = true,
            ["-Tbss"] = true,
            ["-ffixed-x25"] = true,
            ["-MJ"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-save-restore"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fno-profile-generate"] = true,
            ["-ffixed-a3"] = true,
            ["-fapplication-extension"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fmodules-ts"] = true,
            ["-x"] = true,
            ["-fno-common"] = true,
            ["-ffixed-x14"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fsystem-module"] = true,
            ["-ffixed-x1"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fno-show-column"] = true,
            ["-MM"] = true,
            ["-fno-split-stack"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fobjc-weak"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-gline-tables-only"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        clang = false,
        git = [[C:\Program Files\Git\cmd\git.exe]],
        nim = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        zig = false
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            static = true,
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            license = "MIT",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            links = {
                "fmt"
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            version = "10.2.1"
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-seh"] = true,
            ["--kill-at"] = true,
            ["--disable-nxcompat"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-auto-import"] = true,
            ["-static"] = true,
            ["--appcontainer"] = true,
            ["--version"] = true,
            ["-m"] = true,
            ["--gc-sections"] = true,
            ["-o"] = true,
            ["--Bstatic"] = true,
            ["--no-fatal-warnings"] = true,
            ["-L"] = true,
            ["--fatal-warnings"] = true,
            ["--export-all-symbols"] = true,
            ["--strip-debug"] = true,
            ["-s"] = true,
            ["--Bdynamic"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-tsaware"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--verbose"] = true,
            ["--tsaware"] = true,
            ["-dn"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-whole-archive"] = true,
            ["--no-gc-sections"] = true,
            ["-dy"] = true,
            ["--disable-no-seh"] = true,
            ["--disable-auto-import"] = true,
            ["--nxcompat"] = true,
            ["--no-demangle"] = true,
            ["--demangle"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-dynamicbase"] = true,
            ["-l"] = true,
            ["-v"] = true,
            ["--shared"] = true,
            ["-S"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--strip-all"] = true,
            ["--exclude-all-symbols"] = true,
            ["--whole-archive"] = true,
            ["--help"] = true,
            ["--dynamicbase"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--large-address-aware"] = true,
            ["--no-insert-timestamp"] = true
        }
    }
}