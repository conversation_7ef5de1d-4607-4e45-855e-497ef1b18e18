{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            version = "10.2.1",
            links = {
                "fmt"
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            static = true,
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            }
        },
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            version = "v3.11.3",
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            }
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--fatal-warnings"] = true,
            ["--no-fatal-warnings"] = true,
            ["-S"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-seh"] = true,
            ["--disable-no-seh"] = true,
            ["--no-insert-timestamp"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-auto-import"] = true,
            ["--version"] = true,
            ["--disable-auto-import"] = true,
            ["-v"] = true,
            ["--shared"] = true,
            ["-l"] = true,
            ["--strip-debug"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--insert-timestamp"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--help"] = true,
            ["--verbose"] = true,
            ["-L"] = true,
            ["--no-dynamicbase"] = true,
            ["--appcontainer"] = true,
            ["--gc-sections"] = true,
            ["--strip-all"] = true,
            ["-dn"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--tsaware"] = true,
            ["--no-gc-sections"] = true,
            ["--no-demangle"] = true,
            ["--Bstatic"] = true,
            ["--large-address-aware"] = true,
            ["-dy"] = true,
            ["-static"] = true,
            ["--disable-nxcompat"] = true,
            ["-s"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--nxcompat"] = true,
            ["--demangle"] = true,
            ["--Bdynamic"] = true,
            ["--exclude-all-symbols"] = true,
            ["--kill-at"] = true,
            ["-o"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-tsaware"] = true,
            ["--allow-multiple-definition"] = true,
            ["-m"] = true,
            ["--dynamicbase"] = true,
            ["--whole-archive"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mno-ms-bitfields"] = true,
            ["-ftrapv"] = true,
            ["-fshort-enums"] = true,
            ["-mno-hvx"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-print-effective-triple"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-declspec"] = true,
            ["-mlvi-cfi"] = true,
            ["-fno-offload-lto"] = true,
            ["-malign-double"] = true,
            ["-cxx-isystem"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-outline-atomics"] = true,
            ["-emit-llvm"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fstack-size-section"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fcall-saved-x8"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-color-diagnostics"] = true,
            ["--cuda-device-only"] = true,
            ["-ffast-math"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-dsym-dir"] = true,
            ["-mpackets"] = true,
            ["-dependency-dot"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fdata-sections"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mlocal-sdata"] = true,
            ["-mno-nvs"] = true,
            ["-ffixed-a3"] = true,
            ["-mno-cumode"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-x"] = true,
            ["-ffixed-x13"] = true,
            ["-mrecord-mcount"] = true,
            ["-mgpopt"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-x4"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-emit-module"] = true,
            ["-mno-long-calls"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-cl-mad-enable"] = true,
            ["-print-supported-cpus"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-imacros"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-mno-embedded-data"] = true,
            ["-ffixed-d5"] = true,
            ["-fstack-protector"] = true,
            ["-mno-restrict-it"] = true,
            ["-ivfsoverlay"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fdebug-macro"] = true,
            ["-fno-stack-protector"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fconvergent-functions"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fenable-matrix"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-Xassembler"] = true,
            ["-fno-memory-profile"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fno-builtin"] = true,
            ["-mcmse"] = true,
            ["-fuse-line-directives"] = true,
            ["-MQ"] = true,
            ["-fgnu-runtime"] = true,
            ["-Tdata"] = true,
            ["-ffixed-point"] = true,
            ["-o"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-ffinite-loops"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-mms-bitfields"] = true,
            ["-emit-ast"] = true,
            ["-MP"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fmodules-search-all"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-ffixed-x19"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffreestanding"] = true,
            ["--hip-link"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mno-msa"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fintegrated-as"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-mlong-calls"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fgnu89-inline"] = true,
            ["-flto"] = true,
            ["-trigraphs"] = true,
            ["-fno-rtti"] = true,
            ["-gdwarf-3"] = true,
            ["-mno-tgsplit"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mfp32"] = true,
            ["-fno-autolink"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-dI"] = true,
            ["-pedantic"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mlvi-hardening"] = true,
            ["-relocatable-pch"] = true,
            ["-ffixed-x2"] = true,
            ["-isysroot"] = true,
            ["-foffload-lto"] = true,
            ["-freroll-loops"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["--migrate"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-H"] = true,
            ["-fstandalone-debug"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-gline-tables-only"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-femit-all-decls"] = true,
            ["-fxray-link-deps"] = true,
            ["-freg-struct-return"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fcoverage-mapping"] = true,
            ["-mlong-double-64"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-gdwarf-2"] = true,
            ["-ftime-trace"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mno-implicit-float"] = true,
            ["-nobuiltininc"] = true,
            ["-mno-save-restore"] = true,
            ["-MM"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-elide-constructors"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-MJ"] = true,
            ["-finline-hint-functions"] = true,
            ["-ffixed-x24"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-ffixed-d3"] = true,
            ["-fapple-kext"] = true,
            ["-mno-mt"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-trigraphs"] = true,
            ["-fprofile-generate"] = true,
            ["-fdirect-access-external-data"] = true,
            ["--version"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fasync-exceptions"] = true,
            ["-ffixed-d1"] = true,
            ["-fmodules-ts"] = true,
            ["-fembed-bitcode"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-d6"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-ffixed-x3"] = true,
            ["-fslp-vectorize"] = true,
            ["-working-directory"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fno-plt"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-b"] = true,
            ["--cuda-host-only"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-strict-return"] = true,
            ["-emit-interface-stubs"] = true,
            ["-MT"] = true,
            ["-fno-addrsig"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-profile-generate"] = true,
            ["-fcxx-modules"] = true,
            ["-fapplication-extension"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-D"] = true,
            ["-fwritable-strings"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-include"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fcall-saved-x9"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-finite-loops"] = true,
            ["-fshort-wchar"] = true,
            ["-gdwarf32"] = true,
            ["-fobjc-arc"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-ffixed-x5"] = true,
            ["-E"] = true,
            ["-Xopenmp-target"] = true,
            ["-help"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fopenmp-simd"] = true,
            ["-fcall-saved-x18"] = true,
            ["-ffixed-r19"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-freciprocal-math"] = true,
            ["-ffixed-x28"] = true,
            ["-Xclang"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mmt"] = true,
            ["-fsycl"] = true,
            ["-fno-operator-names"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-ffixed-x21"] = true,
            ["-ffixed-x14"] = true,
            ["-pg"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-MMD"] = true,
            ["-fno-jump-tables"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-common"] = true,
            ["-fno-digraphs"] = true,
            ["-ffixed-a6"] = true,
            ["-fno-new-infallible"] = true,
            ["-nogpuinc"] = true,
            ["-verify-pch"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-mmadd4"] = true,
            ["-mcumode"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fmemory-profile"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-ffixed-x10"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-mno-relax"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-isystem"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-P"] = true,
            ["-cl-opt-disable"] = true,
            ["-ibuiltininc"] = true,
            ["--analyzer-output"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-iprefix"] = true,
            ["-munaligned-access"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-a2"] = true,
            ["-z"] = true,
            ["-idirafter"] = true,
            ["-fsanitize-trap"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mpacked-stack"] = true,
            ["-mllvm"] = true,
            ["-dM"] = true,
            ["-mstackrealign"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-ffixed-x25"] = true,
            ["-fcall-saved-x12"] = true,
            ["--emit-static-lib"] = true,
            ["-mno-packets"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-msave-restore"] = true,
            ["-shared-libsan"] = true,
            ["-fdeclspec"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fprotect-parens"] = true,
            ["-fgpu-rdc"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fapprox-func"] = true,
            ["-mabicalls"] = true,
            ["-fforce-enable-int128"] = true,
            ["-mno-extern-sdata"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-MD"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-ffixed-x17"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-gcodeview"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-mno-memops"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-Qn"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-short-wchar"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-print-target-triple"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-x27"] = true,
            ["-fmodules"] = true,
            ["-M"] = true,
            ["-B"] = true,
            ["-fcall-saved-x13"] = true,
            ["-save-stats"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mno-movt"] = true,
            ["-time"] = true,
            ["-fno-fixed-point"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-ffixed-x23"] = true,
            ["-ffixed-x9"] = true,
            ["-fdebug-types-section"] = true,
            ["--config"] = true,
            ["-pipe"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-isystem-after"] = true,
            ["-mnocrc"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-signed-zeros"] = true,
            ["-mno-global-merge"] = true,
            ["-pthread"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-mglobal-merge"] = true,
            ["-fdigraphs"] = true,
            ["-ffixed-x12"] = true,
            ["--help-hidden"] = true,
            ["-L"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mlong-double-128"] = true,
            ["-mseses"] = true,
            ["-fcommon"] = true,
            ["-print-runtime-dir"] = true,
            ["-fopenmp"] = true,
            ["-fstack-usage"] = true,
            ["-faligned-allocation"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-msvr4-struct-return"] = true,
            ["-mcrc"] = true,
            ["-ffixed-a0"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-mno-nvj"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fsave-optimization-record"] = true,
            ["-mibt-seal"] = true,
            ["-mno-local-sdata"] = true,
            ["-mno-gpopt"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mmemops"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-ftrigraphs"] = true,
            ["-ffixed-d2"] = true,
            ["-fno-exceptions"] = true,
            ["-mstack-arg-probe"] = true,
            ["-include-pch"] = true,
            ["-mrelax-all"] = true,
            ["-ffixed-x8"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fintegrated-cc1"] = true,
            ["-static-libsan"] = true,
            ["-Ttext"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fno-split-stack"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-ffixed-x1"] = true,
            ["-CC"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-sycl"] = true,
            ["-fcxx-exceptions"] = true,
            ["-dependency-file"] = true,
            ["-miamcu"] = true,
            ["-membedded-data"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fsized-deallocation"] = true,
            ["-rewrite-objc"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-Qunused-arguments"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-gdwarf64"] = true,
            ["-funroll-loops"] = true,
            ["-mtgsplit"] = true,
            ["-fsanitize-stats"] = true,
            ["-gdwarf-5"] = true,
            ["-rpath"] = true,
            ["-gmodules"] = true,
            ["-fjump-tables"] = true,
            ["-mno-outline"] = true,
            ["-fpascal-strings"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-gdwarf-4"] = true,
            ["-finline-functions"] = true,
            ["-iwithprefix"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fseh-exceptions"] = true,
            ["-ffixed-d4"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-gno-embed-source"] = true,
            ["-mwavefrontsize64"] = true,
            ["-MV"] = true,
            ["-fno-lto"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fminimize-whitespace"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mskip-rax-setup"] = true,
            ["-nogpulib"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-show-source-location"] = true,
            ["-print-targets"] = true,
            ["-undef"] = true,
            ["-fblocks"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fxray-instrument"] = true,
            ["-module-file-info"] = true,
            ["-mthread-model"] = true,
            ["-fcf-protection"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-ffixed-x29"] = true,
            ["-fstrict-enums"] = true,
            ["-mrestrict-it"] = true,
            ["-mhvx"] = true,
            ["-mnvj"] = true,
            ["-I-"] = true,
            ["-mnop-mcount"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-I"] = true,
            ["-ffixed-d7"] = true,
            ["-static-openmp"] = true,
            ["-ffunction-sections"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fwasm-exceptions"] = true,
            ["--analyze"] = true,
            ["-fsystem-module"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-index-header-map"] = true,
            ["-fglobal-isel"] = true,
            ["-fgnu-keywords"] = true,
            ["-mfentry"] = true,
            ["-fno-global-isel"] = true,
            ["-fgpu-sanitize"] = true,
            ["-dD"] = true,
            ["-MF"] = true,
            ["-ffixed-x18"] = true,
            ["-mextern-sdata"] = true,
            ["-mrelax"] = true,
            ["-fvectorize"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-v"] = true,
            ["-nostdinc"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-ffixed-x7"] = true,
            ["-fstack-protector-strong"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-femulated-tls"] = true,
            ["-mrtd"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fpch-codegen"] = true,
            ["-G"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-access-control"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mno-crc"] = true,
            ["-faddrsig"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-F"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ffixed-x30"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mno-madd4"] = true,
            ["-fms-extensions"] = true,
            ["-ffixed-x20"] = true,
            ["-ffixed-x6"] = true,
            ["-print-resource-dir"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fzvector"] = true,
            ["-S"] = true,
            ["-Xanalyzer"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mexecute-only"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fborland-extensions"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-spell-checking"] = true,
            ["-g"] = true,
            ["-fms-compatibility"] = true,
            ["-meabi"] = true,
            ["-print-ivar-layout"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fno-rtti-data"] = true,
            ["-gdwarf"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fsigned-char"] = true,
            ["-ffixed-r9"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-ffixed-x11"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-Xlinker"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-Tbss"] = true,
            ["-MG"] = true,
            ["-fmath-errno"] = true,
            ["-fobjc-exceptions"] = true,
            ["-traditional-cpp"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-arch"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mcode-object-v3"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fcall-saved-x10"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-msoft-float"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fno-temp-file"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-w"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-C"] = true,
            ["--precompile"] = true,
            ["-frwpi"] = true,
            ["-print-multiarch"] = true,
            ["-mnvs"] = true,
            ["-ffixed-x15"] = true,
            ["-mmark-bti-property"] = true,
            ["-gembed-source"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-c"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fnew-infallible"] = true,
            ["-fms-hotpatch"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-moutline"] = true,
            ["-fignore-exceptions"] = true,
            ["-U"] = true,
            ["-ffixed-x22"] = true,
            ["-fno-integrated-as"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-iquote"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mbackchain"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fmodules-decluse"] = true,
            ["-fstack-protector-all"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-save-temps"] = true,
            ["-fno-debug-macro"] = true,
            ["-mno-seses"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fsplit-stack"] = true,
            ["-ffixed-a5"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-Qy"] = true,
            ["-mno-abicalls"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fno-use-init-array"] = true,
            ["-T"] = true,
            ["-fno-show-column"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-module-dependency-dir"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-gline-directives-only"] = true,
            ["-iwithsysroot"] = true,
            ["-extract-api"] = true,
            ["--verify-debug-info"] = true,
            ["-mfp64"] = true,
            ["-Wdeprecated"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fropi"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mmsa"] = true
        }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    find_program = {
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        zig = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        nim = false,
        clang = false
    }
}