{
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    clang_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    zig_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    msvc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndkver = 25,
        __checked = true,
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        arch = "armeabi-v7a",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    }
}