{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android"
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    zig_arch_x64_plat_windows = {
        arch = "x64",
        __checked = false,
        plat = "windows"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        toolname = "clangxx"
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        toolname = "clangxx"
    },
    clang_arch_x64_plat_windows = {
        arch = "x64",
        __checked = false,
        plat = "windows"
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        __checked = false,
        plat = "windows"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __global = true,
        ndkver = 25,
        ndk_sdkver = "21",
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        plat = "android",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
    },
    msvc_arch_x64_plat_windows = {
        arch = "x64",
        __checked = false,
        plat = "windows"
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        plat = "windows"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android"
    }
}