{
    go_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        __global = true,
        arch = "armeabi-v7a",
        plat = "android"
    },
    yasm_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    nasm_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    fpc_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    zig_arch_x64_plat_windows = {
        __checked = false,
        arch = "x64",
        plat = "windows"
    },
    msvc_arch_x64_plat_windows = {
        __checked = false,
        arch = "x64",
        plat = "windows"
    },
    gfortran_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    cuda_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    swift_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    rust_arch_x64_plat_windows = {
        __checked = true,
        arch = "x64",
        plat = "windows"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        plat = "android",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndkver = 25,
        __checked = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        arch = "armeabi-v7a",
        __global = true,
        ndk_sdkver = "21",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        cross = "arm-linux-androideabi-"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        __global = true,
        arch = "armeabi-v7a",
        plat = "android"
    },
    nim_arch_x64_plat_windows = {
        __checked = false,
        arch = "x64",
        plat = "windows"
    },
    clang_arch_x64_plat_windows = {
        __checked = false,
        arch = "x64",
        plat = "windows"
    }
}