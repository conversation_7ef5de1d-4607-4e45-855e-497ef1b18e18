{
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        __global = true,
        arch = "armeabi-v7a"
    },
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        }
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        }
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndkver = 25,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __global = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true,
        plat = "android"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        __global = true,
        arch = "armeabi-v7a"
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    }
}