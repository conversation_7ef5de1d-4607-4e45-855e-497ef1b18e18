{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx"
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        cross = "arm-linux-androideabi-",
        __global = true,
        arch = "armeabi-v7a",
        ndk_sdkver = "21",
        ndkver = 25,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        plat = "android",
        __checked = true
    }
}