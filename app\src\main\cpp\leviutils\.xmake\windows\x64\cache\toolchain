{
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        arch = "armeabi-v7a",
        __global = true,
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndkver = 25
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        __global = true,
        arch = "armeabi-v7a"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        __global = true,
        arch = "armeabi-v7a"
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}