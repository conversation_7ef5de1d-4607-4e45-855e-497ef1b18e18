{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        plat = "android",
        __checked = true
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        plat = "android",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        __global = true,
        ndkver = 25,
        __checked = true,
        arch = "armeabi-v7a",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk",
            plat = "android"
        }
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk",
            plat = "android"
        }
    }
}