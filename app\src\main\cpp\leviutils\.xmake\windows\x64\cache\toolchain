{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        arch = "armeabi-v7a",
        __global = true,
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk_sdkver = "21",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        cross = "arm-linux-androideabi-",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        arch = "armeabi-v7a",
        __global = true,
        ndkver = 25
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        arch = "armeabi-v7a",
        __global = true,
        plat = "android"
    }
}