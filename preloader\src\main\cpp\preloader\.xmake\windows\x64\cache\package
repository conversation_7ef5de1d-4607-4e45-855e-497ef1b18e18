{
    nlohmann_json = {
        __requirestr = "nlohmann_json v3.11.3",
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6]],
        __enabled = true,
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]],
        license = "MIT",
        version = "v3.11.3"
    },
    fmt = {
        static = true,
        links = "fmt",
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36]],
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\f99acdf63a7d4fc88c165b22351691b6\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.3\5249d11949644178abe2f58a6ffb0624\bin]]
            }
        },
        linkdirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]],
        license = "MIT",
        libfiles = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]],
        __enabled = true,
        version = "10.2.1",
        __requirestr = "fmt 10",
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
    }
}