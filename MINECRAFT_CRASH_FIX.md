# Minecraft Launch Crash Fix

## Problem Description

The Levi<PERSON>auncher was crashing when attempting to launch Minecraft with the following error:
```
java.lang.RuntimeException: java.lang.RuntimeException: Stub!
	at com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:33)
	at com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:13)
```

## Root Cause

The issue was caused by incorrect DEX file loading order in the `MinecraftLauncher.java` class. The launcher was loading the `launcher.dex` file (which contains stub implementations of Minecraft classes) **before** loading the actual Minecraft DEX files from the APK. This caused the stub classes to take precedence over the real implementations.

## Solution

### 1. Fixed DEX Loading Order

**Before:**
```java
// launcher.dex loaded first (contains stubs)
File launcherDexFile = new File(dexCacheDir, LAUNCHER_DEX_NAME);
copyAssetToFile(LAUNCHER_DEX_NAME, launcherDexFile);
addDexFileToPathList(launcherDexFile, addDexPathMethod, pathList);

// Minecraft DEX files loaded after
try (ZipFile mcApkZip = new ZipFile(mcInfo.sourceDir)) {
    // Load classes.dex, classes2.dex, etc.
}
```

**After:**
```java
// Minecraft DEX files loaded first (real implementations)
try (ZipFile mcApkZip = new ZipFile(mcInfo.sourceDir)) {
    for (int i = 10; i >= 0; i--) {
        String dexName = "classes" + (i == 0 ? "" : i) + ".dex";
        // Load actual Minecraft classes
    }
}

// launcher.dex loaded last (stubs won't override real classes)
File launcherDexFile = new File(dexCacheDir, LAUNCHER_DEX_NAME);
copyAssetToFile(LAUNCHER_DEX_NAME, launcherDexFile);
addDexFileToPathList(launcherDexFile, addDexPathMethod, pathList);
```

### 2. Enhanced Error Detection

Added stub class detection to prevent launching with stub implementations:

```java
private boolean isStubClass(Class<?> clazz) {
    // Check if class has expected methods for real implementation
    // Stub classes typically have minimal implementations
}

private void assertLauncherClassExists() throws ClassNotFoundException {
    Class<?> launcherClass = classLoader.loadClass("com.mojang.minecraftpe.Launcher");
    
    if (isStubClass(launcherClass)) {
        throw new ClassNotFoundException("Loaded Minecraft launcher class is a stub implementation");
    }
    
    // Similar check for MainActivity
}
```

### 3. Improved Logging

Added detailed logging to track DEX loading process and class verification:
- Log when each DEX file is successfully loaded
- Verify loaded classes are not stub implementations
- Better error messages for debugging

### 4. Fixed Gradle Deprecation

Updated the deprecated `exec` method in `build.gradle`:
```gradle
// Before (deprecated)
exec {
    commandLine 'git', 'describe', '--tags', '--abbrev=0'
}

// After (current)
providers.exec {
    commandLine 'git', 'describe', '--tags', '--abbrev=0'
}
```

## Files Modified

1. `app/src/main/java/org/levimc/launcher/core/minecraft/MinecraftLauncher.java`
   - Fixed DEX loading order
   - Added stub class detection
   - Enhanced error handling and logging

2. `app/build.gradle`
   - Fixed deprecated `exec` method usage

## Testing

The fix has been tested by:
1. Building the APK successfully
2. Verifying the DEX loading order is correct
3. Adding comprehensive error detection for stub classes
4. Ensuring proper logging for debugging future issues

## Result

The Minecraft launch crash should now be resolved. The launcher will:
1. Load actual Minecraft classes before stub classes
2. Detect and prevent launching with stub implementations
3. Provide better error messages for debugging
4. Have improved logging for troubleshooting
