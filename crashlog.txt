2025-07-30 05:26:46.512 12838-12914 ProfileInstaller        org.levimc.launcher                  D  Installing profile for org.levimc.launcher
2025-07-30 05:26:46.693 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-30 05:26:46.699 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@815b2e0
2025-07-30 05:26:46.759 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-30 05:26:46.769 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-30 05:26:46.771 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes2.dex
2025-07-30 05:26:46.773 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes.dex
2025-07-30 05:26:46.773 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: launcher.dex
2025-07-30 05:26:46.773 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.773 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.790 12838-12838 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-30 05:26:46.817 12838-12838 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@fd5cd3f
2025-07-30 05:26:46.818 12838-12838 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@3ab896a
2025-07-30 05:26:46.818 12838-12838 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{5e3fe5b V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:328 
2025-07-30 05:26:46.820 12838-12838 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 05:26:46.821 12838-12855 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 05:26:46.831 12838-12838 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'd262fef', fd=140
2025-07-30 05:26:46.831 12838-12838 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:26:46.832 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 05:26:46.832 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-30 05:26:46.833 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@5e3fe5b IsHRR=false TM=true
2025-07-30 05:26:46.847 12838-12838 BufferQueueConsumer     org.levimc.launcher                  D  [](id:322600000002,api:0,p:-1,c:12838) connect: controlledByApp=false
2025-07-30 05:26:46.848 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) constructor()
2025-07-30 05:26:46.848 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@ef536f8 mNativeObject= 0xb400007988181400 sc.mNativeObject= 0xb4000079883557c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 05:26:46.848 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@ef536f8 mNativeObject= 0xb400007988181400 sc.mNativeObject= 0xb4000079883557c0 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 05:26:46.848 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-30 05:26:46.848 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=7 res=0x3 s={true 0xb4000079e59bd000} ch=true seqId=0
2025-07-30 05:26:46.849 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 05:26:46.849 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079e59bd000} hwInitialized=true
2025-07-30 05:26:46.850 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 05:26:46.850 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@ef536f8#6
2025-07-30 05:26:46.850 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@ef536f8#7
2025-07-30 05:26:46.854 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:26:46.856 12838-12876 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 05:26:46.856 12838-12876 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  mWNT: t=0xb4000079883ce780 mBlastBufferQueue=0xb400007988181400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 05:26:46.856 12838-12876 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:26:46.858 12838-12855 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:26:46.859 12838-12855 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=292403827394966(auto) mPendingTransactions.size=0 graphicBufferId=55138790146073 transform=7
2025-07-30 05:26:46.859 12838-12855 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 05:26:46.861 12838-12855 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 12838    Tid : 12855
2025-07-30 05:26:46.861 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:26:46.862 12838-12855 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:26:46.884 12838-12838 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:26:46.885 12838-12838 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:26:46.887 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-30 05:26:46.887 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: classes2.dex
2025-07-30 05:26:46.920 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e59bd000}
2025-07-30 05:26:46.920 12838-12838 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 05:26:46.920 12838-12838 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 05:26:46.925 12838-12849 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=159
2025-07-30 05:26:46.930 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-30 05:26:46.935 12838-12838 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-30 05:26:46.977 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-30 05:26:46.977 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: classes.dex
2025-07-30 05:26:46.978 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: launcher.dex
2025-07-30 05:26:46.978 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: launcher.dex
2025-07-30 05:26:46.978 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: launcher.dex
2025-07-30 05:26:46.981 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded non-stub Minecraft classes: Launcher and MainActivity
2025-07-30 05:26:46.981 12838-12916 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-30 05:26:46.981 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.981 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.981 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.981 12838-12916 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:26:46.982 12838-12919 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded launcher class: com.mojang.minecraftpe.Launcher
2025-07-30 05:26:46.983 12838-12919 LeviLogger              org.levimc.launcher                  I  [LeviMC] Launcher class has onCreate method - not a stub
2025-07-30 05:26:46.984 12838-12919 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk!classes7.dex): ok
2025-07-30 05:26:46.987 12838-12919 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk!classes7.dex): ok
2025-07-30 05:26:47.089 12838-12919 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk!classes7.dex): ok
2025-07-30 05:26:47.089 12838-12919 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x78d8d0b5f0
2025-07-30 05:26:47.089 12838-12919 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-30 05:26:47.140 12838-12838 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 05:26:47.150 12838-12838 nativeloader            org.levimc.launcher                  D  Load libmaesdk.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libmaesdk.so" not found
2025-07-30 05:26:47.150 12838-12838 MCPE                    org.levimc.launcher                  D  maesdk library not found. This is expected if we're not in Edu mode
2025-07-30 05:26:47.151 12838-12838 nativeloader            org.levimc.launcher                  D  Load libovrfmod.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrfmod.so" not found
2025-07-30 05:26:47.151 12838-12838 MCPE                    org.levimc.launcher                  D  OVRfmod library not found
2025-07-30 05:26:47.152 12838-12838 nativeloader            org.levimc.launcher                  D  Load libovrplatformloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrplatformloader.so" not found
2025-07-30 05:26:47.152 12838-12838 MCPE                    org.levimc.launcher                  D  OVRplatform library not found
2025-07-30 05:26:47.154 12838-12838 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-30 05:26:47.154 12838-12838 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-30 05:26:47.154 12838-12838 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-30 05:26:47.154 12838-12838 nativeloader            org.levimc.launcher                  D  Load /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/launcher.dex): ok
2025-07-30 05:26:47.162 12838-12838 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-30 05:26:47.168 12838-12838 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::onCreate
2025-07-30 05:26:47.174 12838-12838 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@afac1b3
2025-07-30 05:26:47.177 12838-12838 gti.InputConnection     org.levimc.launcher                  D  InputConnection created
2025-07-30 05:26:47.181 12838-12838 GameActivity            org.levimc.launcher                  I  Looking for library libpreloader.so
2025-07-30 05:26:47.181 12838-12838 GameActivity            org.levimc.launcher                  I  Found library libpreloader.so. Loading...
2025-07-30 05:26:47.186 12838-12838 GameActivity            org.levimc.launcher                  D  GameActivity_register
2025-07-30 05:26:47.186 12838-12838 GameActivity            org.levimc.launcher                  D  SDK version: 35
2025-07-30 05:26:47.187 12838-12922 Minecraft               org.levimc.launcher                  I  android_main starting. internalDataPath is '/data/user/0/org.levimc.launcher/files', externalDataPath is '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-30 05:26:47.192 12838-12922 MCPE                    org.levimc.launcher                  E  *** setCachedDeviceId(307ff7a867a04f2487c5f1e9fa054bb1)
2025-07-30 05:26:47.194 12838-12922 Bedrock                 org.levimc.launcher                  I  Breakpad config: directory is: /data/user/0/org.levimc.launcher/crash, sessionid is: 7f1bd4c5-9141-41c1-b8fc-b15bdd6a06ed
2025-07-30 05:26:47.197 12838-12922 libc                    org.levimc.launcher                  W  Access denied finding property "ro.mediatek.platform"
2025-07-30 05:26:47.205 12838-12922 System.out              org.levimc.launcher                  I  getwidth: 2340
2025-07-30 05:26:47.205 12838-12922 System.out              org.levimc.launcher                  I  getheight: 1080
2025-07-30 05:26:47.211 12838-12922 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-30 05:26:47.264 12838-12838 AppExitInfoHelper       org.levimc.launcher                  I  Registering session ID for ApplicationExitInfo: 7f1bd4c5-9141-41c1-b8fc-b15bdd6a06ed
2025-07-30 05:26:47.274 12838-12838 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze.configure() called with configuration: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:26:47.275 12838-12838 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::requestPushPermission
2025-07-30 05:26:47.276 12838-12922 SwappyDisplayManager    org.levimc.launcher                  I  Using internal com/google/androidgamesdk/SwappyDisplayManager class from dex bytes.
2025-07-30 05:26:47.278 12838-12922 SwappyDisplayManager    org.levimc.launcher                  E  dalvik.system.InMemoryDexClassLoader[DexPathList[[dex file "InMemoryDexFile[cookie=[0, -5476376627837448992]]"],nativeLibraryDirectories=[/system/lib64, /system_ext/lib64]]] couldn't find "libpreloader.so"
2025-07-30 05:26:47.280 12838-12926 SwappyDisplayManager    org.levimc.launcher                  I  Starting looper thread
2025-07-30 05:26:47.281 12838-12922 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000078d96db200
2025-07-30 05:26:47.281 12838-12922 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-30 05:26:47.283 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:31)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:26:47.290 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:32)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:26:47.294 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:33)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:26:47.299 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:34)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:26:47.302 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:37)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:26:47.319 12838-12838 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-30 05:26:47.320 12838-12838 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 303326708; UID 10645; state: ENABLED
2025-07-30 05:26:47.321 12838-12838 MinecraftPE             org.levimc.launcher                  D  onStart
2025-07-30 05:26:47.329 12838-12922 AppExitInfoHelper       org.levimc.launcher                  I  Received session ID from ApplicationExitInfo: a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee
2025-07-30 05:26:47.338 12838-12922 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id -1- is Crete Controller: false
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id 2- is Crete Controller: false
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id 3- is Crete Controller: false
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id 4- is Crete Controller: false
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id 5- is Crete Controller: false
2025-07-30 05:26:47.343 12838-12838 Minecraft               org.levimc.launcher                  W  INPUT device id 6- is Crete Controller: false
2025-07-30 05:26:47.344 12838-12838 Minecraft               org.levimc.launcher                  W  No Xbox Controller Found
2025-07-30 05:26:47.344 12838-12838 Minecraft               org.levimc.launcher                  W  No Playstation Controller Found
2025-07-30 05:26:47.344 12838-12838 Minecraft               org.levimc.launcher                  W  No PS Dualsense Controller Found
2025-07-30 05:26:47.349 12838-12838 MinecraftPE             org.levimc.launcher                  D  onResume
2025-07-30 05:26:47.360 12838-12838 Braze v24....ageManager org.levimc.launcher                  V  Registering InAppMessageManager with activity: com.mojang.minecraftpe.Launcher
2025-07-30 05:26:47.363 12838-12838 Braze v24....ageManager org.levimc.launcher                  D  Subscribing in-app message event subscriber
2025-07-30 05:26:47.363 12838-12838 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-30 05:26:47.363 12838-12838 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-30 05:26:47.363 12838-12838 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK Initializing
2025-07-30 05:26:47.381 12838-12939 Braze v24....mageLoader org.levimc.launcher                  D  Initializing disk cache
2025-07-30 05:26:47.386 12838-12838 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK loaded in 22 ms.
2025-07-30 05:26:47.386 12838-12838 Braze v24....ageManager org.levimc.launcher                  V  Subscribing sdk data wipe subscriber
2025-07-30 05:26:47.387 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Applying any pending runtime configuration values
2025-07-30 05:26:47.387 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Setting pending config object: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:26:47.387 12838-12943 Braze v24....onProvider org.levimc.launcher                  I  Setting Braze Override configuration with config: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:26:47.387 12838-12939 Braze v24....mageLoader org.levimc.launcher                  D  Disk cache initialized
2025-07-30 05:26:47.388 12838-12943 Braze v24....onProvider org.levimc.launcher                  I  Found an override api key. Using it to configure the Braze SDK
2025-07-30 05:26:47.391 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml INTEGER configuration value with primary key 'com_braze_logger_initial_log_level'. Using default value '4'.
2025-07-30 05:26:47.391 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_logger_initial_log_level' and value: '4'
2025-07-30 05:26:47.392 12838-12838 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 05:26:47.394 12838-12855 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 05:26:47.396 12838-12838 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-30 05:26:47.404 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_custom_endpoint'. Using default value 'null'.
2025-07-30 05:26:47.404 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_endpoint' and value: 'null'
2025-07-30 05:26:47.404 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_firebase_cloud_messaging_registration_enabled' and value: 'false'
2025-07-30 05:26:47.404 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic Firebase Cloud Messaging registration not enabled in configuration. Braze will not register for Firebase Cloud Messaging.
2025-07-30 05:26:47.404 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_push_adm_messaging_registration_enabled' and value: 'false'
2025-07-30 05:26:47.404 12838-12838 InputTransport          org.levimc.launcher                  D  Input channel constructed: '392bf45', fd=221
2025-07-30 05:26:47.404 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic ADM registration not enabled in configuration. Braze will not register for ADM.
2025-07-30 05:26:47.405 12838-12838 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:26:47.405 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 05:26:47.405 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@84e5cb7 IsHRR=false TM=true
2025-07-30 05:26:47.406 12838-12838 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:26:47.406 12838-12838 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:26:47.406 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Starting up a new user dependency manager
2025-07-30 05:26:47.407 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  onWindowVisibilityChanged(0) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{4f58f6e V.E...... ......I. 0,0-0,0} of VRI[Launcher]@2a894bf
2025-07-30 05:26:47.407 12838-12838 SurfaceView             org.levimc.launcher                  D  83201902 updateSurface: has no frame
2025-07-30 05:26:47.409 12838-12838 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:26:47.409 12838-12838 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:26:47.426 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_session_timeout' and value: '10'
2025-07-30 05:26:47.426 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_session_start_based_timeout_enabled'. Using default value 'false'.
2025-07-30 05:26:47.426 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_session_start_based_timeout_enabled' and value: 'false'
2025-07-30 05:26:47.441 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_trigger_action_minimum_time_interval_seconds' and value: '5'
2025-07-30 05:26:47.442 12838-12838 BufferQueueConsumer     org.levimc.launcher                  D  [](id:322600000003,api:0,p:-1,c:12838) connect: controlledByApp=false
2025-07-30 05:26:47.442 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@2a894bf#3](f:0,a:0,s:0) constructor()
2025-07-30 05:26:47.442 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@2a894bf mNativeObject= 0xb4000079e5a27400 sc.mNativeObject= 0xb4000078d97a0700 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 05:26:47.442 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@2a894bf mNativeObject= 0xb4000079e5a27400 sc.mNativeObject= 0xb4000078d97a0700 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 05:26:47.442 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@2a894bf#3](f:0,a:0,s:0) update width=2340 height=1080 format=-3 mTransformHint=4
2025-07-30 05:26:47.443 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=31 res=0x3 s={true 0xb4000078d94f3000} ch=true seqId=0
2025-07-30 05:26:47.443 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 05:26:47.446 12838-12838 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:26:47.446 12838-12838 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:26:47.448 12838-12943 Braze v24.....bo.app.d6 org.levimc.launcher                  V  Subscribing to trigger dispatch events.
2025-07-30 05:26:47.448 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078d94f3000} hwInitialized=true
2025-07-30 05:26:47.448 12838-12838 SurfaceView             org.levimc.launcher                  D  83201902 updateSurface: has no frame
2025-07-30 05:26:47.449 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  windowStopped(false) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{4f58f6e V.E...... ......ID 0,0-2340,1080} of VRI[Launcher]@2a894bf
2025-07-30 05:26:47.449 12838-12838 SurfaceView             org.levimc.launcher                  D  83201902 updateSurface: has no frame
2025-07-30 05:26:47.450 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 05:26:47.450 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 Changes: creating=true format=true size=true visible=true alpha=false hint=true visible=true left=true top=true z=false attached=true lifecycleStrategy=false
2025-07-30 05:26:47.452 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_custom_location_providers_list'. Using default value '[]'.
2025-07-30 05:26:47.452 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_location_providers_list' and value: '[]'
2025-07-30 05:26:47.454 12838-12943 Braze v24....nceManager org.levimc.launcher                  D  Did not find stored geofences.
2025-07-30 05:26:47.455 12838-12838 BufferQueueConsumer     org.levimc.launcher                  D  [](id:322600000004,api:0,p:-1,c:12838) connect: controlledByApp=false
2025-07-30 05:26:47.456 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) constructor()
2025-07-30 05:26:47.456 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = null mNativeObject= 0xb400007988181800 sc.mNativeObject= 0xb4000078f6c0c300 format= 4 caller= android.view.SurfaceView.createBlastSurfaceControls:1642 android.view.SurfaceView.updateSurface:1318 android.view.SurfaceView.lambda$new$0:268 android.view.SurfaceView.$r8$lambda$NfZyM_TG8F8lqzaOVZ7noREFjzU:0 android.view.SurfaceView$$ExternalSyntheticLambda1.onPreDraw:0 android.view.ViewTreeObserver.dispatchOnPreDraw:1226 
2025-07-30 05:26:47.456 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-30 05:26:47.456 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 Cur surface: Surface(name=null mNativeObject=0)/@0xaf256f2
2025-07-30 05:26:47.456 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  pST: sr = Rect(0, 0 - 2340, 1080) sw = 2340 sh = 1080
2025-07-30 05:26:47.456 12838-12838 SurfaceView             org.levimc.launcher                  D  83201902 performSurfaceTransaction RenderWorker position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-30 05:26:47.457 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  updateSurface: mVisible = true mSurface.isValid() = true
2025-07-30 05:26:47.457 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  updateSurface: mSurfaceCreated = false surfaceChanged = true visibleChanged = true
2025-07-30 05:26:47.457 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 visibleChanged -- surfaceCreated
2025-07-30 05:26:47.457 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  surfaceCreated 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{4f58f6e V.E...... ......ID 0,0-2340,1080}
2025-07-30 05:26:47.457 12838-12943 Braze v24....nceManager org.levimc.launcher                  I  Geofences implicitly disabled via server configuration.
2025-07-30 05:26:47.457 12838-12943 Braze v24....nceManager org.levimc.launcher                  I  ***Geofence API not found. Please include the android-sdk-location module***
2025-07-30 05:26:47.457 12838-12943 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Geofences not set up.
2025-07-30 05:26:47.457 12838-12943 Braze v24.3.0 .o        org.levimc.launcher                  I  ***Location API not found. Please include android-sdk-location module***
2025-07-30 05:26:47.459 12838-12943 Braze v24.3.0 .f1       org.levimc.launcher                  D  Did not find stored feature flags.
2025-07-30 05:26:47.478 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: NONE
2025-07-30 05:26:47.478 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:26:47.478 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  D  Data sync started
2025-07-30 05:26:47.479 12838-12943 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5461)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5428)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5402)] [bo.app.i0.d(SourceFile:3)] [bo.app.i0.e(SourceFile:6)] [bo.app.i0.a(SourceFile:10)] [bo.app.n6.<init>(SourceFile:227)] [com.braze.Braze$d.a(SourceFile:63)] [com.braze.Braze$d.invoke(SourceFile:1)] [com.braze.Braze$r2$a.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:284)] [kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:85)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:59)] [kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source:1)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:38)] [kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source:1)] [com.braze.Braze$r2.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)] [java.lang.Thread.run(Thread.java:1119)]
2025-07-30 05:26:47.484 12838-12928 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:26:47.484 12838-12928 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:26:47.484 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:26:47.484 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:26:47.489 12838-12922 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-30 05:26:47.490 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 surfaceChanged -- format=4 w=2340 h=1080
2025-07-30 05:26:47.490 12838-12838 SurfaceView@4f58f6e     org.levimc.launcher                  I  surfaceChanged (2340,1080) 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{4f58f6e V.E...... ......ID 0,0-2340,1080}
2025-07-30 05:26:47.490 12838-12943 Braze v24.3.0 .c1       org.levimc.launcher                  D  Started offline event recovery task.
2025-07-30 05:26:47.490 12838-12922 Minecraft               org.levimc.launcher                  W  MinecraftGame::init && MinecraftGame::setSize!
2025-07-30 05:26:47.490 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 surfaceRedrawNeeded
2025-07-30 05:26:47.491 12838-12838 SurfaceView             org.levimc.launcher                  I  83201902 finishedDrawing
2025-07-30 05:26:47.491 12838-12838 SurfaceView             org.levimc.launcher                  V  Layout: x=0 y=0 w=2340 h=1080, frame=Rect(0, 0 - 2340, 1080)
2025-07-30 05:26:47.492 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@2a894bf#9
2025-07-30 05:26:47.492 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@2a894bf#10
2025-07-30 05:26:47.493 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:26:47.494 12838-12855 SurfaceView             org.levimc.launcher                  D  83201902 updateSurfacePosition RenderWorker, frameNr = 1, position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-30 05:26:47.494 12838-12855 SurfaceView@4f58f6e     org.levimc.launcher                  I  uSP: rtp = Rect(0, 0 - 2340, 1080) rtsw = 2340 rtsh = 1080
2025-07-30 05:26:47.494 12838-12855 SurfaceView@4f58f6e     org.levimc.launcher                  I  onSSPAndSRT: pl = 0 pt = 0 sx = 1.0 sy = 1.0
2025-07-30 05:26:47.494 12838-12855 SurfaceView@4f58f6e     org.levimc.launcher                  I  aOrMT: VRI[Launcher]@2a894bf t = android.view.SurfaceControl$Transaction@f604d23 fN = 1 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 android.graphics.RenderNode$CompositePositionUpdateListener.positionChanged:398 
2025-07-30 05:26:47.494 12838-12855 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  mWNT: t=0xb4000078d98dba00 mBlastBufferQueue=0xb4000079e5a27400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SurfaceView.applyOrMergeTransaction:1723 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 
2025-07-30 05:26:47.495 12838-12875 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 05:26:47.495 12838-12875 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  mWNT: t=0xb4000078f6bfe300 mBlastBufferQueue=0xb4000079e5a27400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 05:26:47.495 12838-12875 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:26:47.498 12838-12935 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000078dca79f40
2025-07-30 05:26:47.498 12838-12935 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-30 05:26:47.501 12838-12855 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@2a894bf#3](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:26:47.502 12838-12855 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@2a894bf#3](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=292404470138043(auto) mPendingTransactions.size=0 graphicBufferId=55138790146084 transform=7
2025-07-30 05:26:47.502 12838-12855 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 05:26:47.503 12838-12855 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 12838    Tid : 12855
2025-07-30 05:26:47.503 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:26:47.512 12838-12855 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:26:47.515 12838-12838 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.performTraversals:4497 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-30 05:26:47.516 12838-12838 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@2a894bf mNativeObject= 0xb4000079e5a27400 sc.mNativeObject= 0xb4000078d97a0700 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-30 05:26:47.516 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=1 res=0x0 s={true 0xb4000078d94f3000} ch=false seqId=0
2025-07-30 05:26:47.517 12838-12838 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:26:47.517 12838-12838 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:26:47.519 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@8d7659e sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0xae0987f frame=2
2025-07-30 05:26:47.520 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-30 05:26:47.521 12838-12876 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  mWNT: t=0xb4000078f6bfe480 mBlastBufferQueue=0xb4000079e5a27400 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-30 05:26:47.534 12838-12838 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:26:47.535 12838-12838 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:26:47.536 12838-12838 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:26:47.538 12838-12838 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:26:47.539 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-30 05:26:47.539 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-30 05:26:47.539 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-30 05:26:47.541 12838-12838 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:26:47.541 12838-12838 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:26:47.542 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@2a894bf#11
2025-07-30 05:26:47.542 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@2a894bf#12
2025-07-30 05:26:47.543 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:26:47.544 12838-12875 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-30 05:26:47.544 12838-12875 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:26:47.550 12838-12855 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-30 05:26:47.551 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:26:47.556 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-30 05:26:47.556 12838-12838 VRI[Launcher]@2a894bf   org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078d94f3000}
2025-07-30 05:26:47.558 12838-12838 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 05:26:47.558 12838-12838 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 05:26:47.570 12838-12838 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.xbox.httpclient.NetworkObserver.Initialize(NetworkObserver.java:72)] [com.mojang.minecraftpe.MainActivity.nativeRunNativeCallbackOnUiThread(Native Method)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2136)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2133)] [java.util.concurrent.FutureTask.run(FutureTask.java:317)]
2025-07-30 05:26:47.574 12838-12850 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=162
2025-07-30 05:26:47.585 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:47.585 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"8a991c55-7953-49b3-9d0d-a5860a37ce47","versionCode":"972109401"}}
2025-07-30 05:26:47.587 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:47.587 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:47.587 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:47.587 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:47.587 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:47.587 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:47.587 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp 2 more time(s) after 1000 ms
2025-07-30 05:26:47.593 12838-12838 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:26:47.596 12838-12933 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 312399441; UID 10645; state: ENABLED
2025-07-30 05:26:47.622 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-30 05:26:47.623 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-30 05:26:47.623 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-30 05:26:47.623 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-30 05:26:47.636 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=6 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-30 05:26:47.637 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-30 05:26:47.637 12838-12905 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@ef536f8#2](f:0,a:3,s:0) destructor()
2025-07-30 05:26:47.637 12838-12905 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@ef536f8#2(BLAST Consumer)2](id:322600000002,api:0,p:-1,c:12838) disconnect
2025-07-30 05:26:47.647 12838-12838 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@815b2e0#1](f:0,a:3,s:0) destructor()
2025-07-30 05:26:47.647 12838-12838 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@815b2e0#1(BLAST Consumer)1](id:322600000001,api:0,p:-1,c:12838) disconnect
2025-07-30 05:26:47.648 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=5 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-30 05:26:47.649 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-30 05:26:47.650 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  stopped(true) old = false
2025-07-30 05:26:47.651 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-30 05:26:47.651 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  stopped(true) old = false
2025-07-30 05:26:47.651 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-30 05:26:47.653 12838-12922 BitmapFactory           org.levimc.launcher                  E  Unable to decode file: java.io.FileNotFoundException: /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png: open failed: ENOENT (No such file or directory)
2025-07-30 05:26:47.653 12838-12922 System.err              org.levimc.launcher                  W  getImageData: Could not open image /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png
2025-07-30 05:26:47.654 12838-12943 Braze v24.3.0 .q        org.levimc.launcher                  D  Messaging session stopped. Adding new messaging session timestamp: 1753831607
2025-07-30 05:26:47.654 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  V  Closed session with activity: ui.activities.MainActivity
2025-07-30 05:26:47.654 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Getting the stored open session
2025-07-30 05:26:47.657 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: f120af68-cc62-47fc-9f15-085d8993ceab
2025-07-30 05:26:47.658 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  I  Session [f120af68-cc62-47fc-9f15-085d8993ceab] being sealed because its end time is over the grace period. Session: 
                                                                                                    MutableSession(sessionId=f120af68-cc62-47fc-9f15-085d8993ceab, startTime=1.753831588137E9, endTime=1.753831588153E9, isSealed=false, duration=0)
2025-07-30 05:26:47.660 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.f5 fired: SessionSealedEvent(sealedSession=
                                                                                                    MutableSession(sessionId=f120af68-cc62-47fc-9f15-085d8993ceab, startTime=1.753831588137E9, endTime=1.753831607658E9, isSealed=true, duration=19))
2025-07-30 05:26:47.660 12838-12838 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@a1afdf8
2025-07-30 05:26:47.660 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.f5 on 1 subscribers.
2025-07-30 05:26:47.662 12838-12838 VRI[MainAc...y]@815b2e0 org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-30 05:26:47.666 12838-12838 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'c19f9b3', fd=206
2025-07-30 05:26:47.666 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding session id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 19
                                                                                                      },
                                                                                                      "time": 1.753831607665E9,
                                                                                                      "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                    }
2025-07-30 05:26:47.667 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 19
                                                                                                      },
                                                                                                      "time": 1.753831607665E9,
                                                                                                      "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                    }
2025-07-30 05:26:47.667 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 19
                                                                                                      },
                                                                                                      "time": 1.753831607665E9,
                                                                                                      "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                    }
2025-07-30 05:26:47.668 12838-12838 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{5e3fe5b V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:328)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-30 05:26:47.668 12838-12838 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{5e3fe5b V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-30 05:26:47.668 12838-12939 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 1cfb8541-2e29-4ee0-85db-34159a6a4790
2025-07-30 05:26:47.670 12838-12838 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@5da2537
2025-07-30 05:26:47.671 12838-12838 VRI[MainAc...y]@ef536f8 org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-30 05:26:47.674 12838-12838 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'd262fef', fd=140
2025-07-30 05:26:47.675 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"se","data":{"d":19},"time":1.753831607665E9,"session_id":"f120af68-cc62-47fc-9f15-085d8993ceab"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:26:47.675 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:47.677 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='f120af68-cc62-47fc-9f15-085d8993ceab', eventType='SESSION_ENDED'}'
2025-07-30 05:26:47.677 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:26:47.678 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  I  New session created with ID: f5d1b92c-f849-411b-a69e-e74d36aecf6a
2025-07-30 05:26:47.678 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.d5 fired: SessionCreatedEvent(session=
                                                                                                    MutableSession(sessionId=f5d1b92c-f849-411b-a69e-e74d36aecf6a, startTime=1.753831607677E9, endTime=null, isSealed=false, duration=-1))
2025-07-30 05:26:47.678 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.d5 on 2 subscribers.
2025-07-30 05:26:47.678 12838-12943 Braze v24.3.0 .z0       org.levimc.launcher                  D  Session start event for new session received.
2025-07-30 05:26:47.679 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: f5d1b92c-f849-411b-a69e-e74d36aecf6a
2025-07-30 05:26:47.679 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: f5d1b92c-f849-411b-a69e-e74d36aecf6a
2025-07-30 05:26:47.679 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753831607678E9,
                                                                                                      "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                    }
2025-07-30 05:26:47.679 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753831607678E9,
                                                                                                      "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                    }
2025-07-30 05:26:47.680 12838-12939 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid e4234343-b386-45ec-afd1-529fd732380c
2025-07-30 05:26:47.681  1598-1804  WindowManager           system_server                        E  win=Window{d262fef u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17 
2025-07-30 05:26:47.682 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"ss","data":{},"time":1.753831607678E9,"session_id":"f5d1b92c-f849-411b-a69e-e74d36aecf6a"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:26:47.682 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:47.683 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = FLUSH_PENDING_BRAZE_EVENTS
                                                                                                    brazeEvent = null
                                                                                                    sessionId = f5d1b92c-f849-411b-a69e-e74d36aecf6a
                                                                                                    brazeRequest = null
2025-07-30 05:26:47.683 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:47.683 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: true
2025-07-30 05:26:47.683 12838-12943 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:26:47.684 12838-12943 Braze v24.3.0 .l0       org.levimc.launcher                  V  Device object cache cleared.
2025-07-30 05:26:47.684 12838-12943 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting trigger refresh.
2025-07-30 05:26:47.685 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_server_target'. Using default value 'PROD'.
2025-07-30 05:26:47.686 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_server_target' and value: 'PROD'
2025-07-30 05:26:47.692 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:26:47.692 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:47.693 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.y5 fired: TriggerDispatchStartedEvent(request={
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:47.693 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.y5 on 1 subscribers.
2025-07-30 05:26:47.694 12838-12943 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:26:47.695 12838-12943 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:26:47.695 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_automatic_geofence_requests_enabled'. Using default value 'true'.
2025-07-30 05:26:47.695 12838-12943 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_automatic_geofence_requests_enabled' and value: 'true'
2025-07-30 05:26:47.695 12838-12943 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting Braze Geofence refresh on session created event due to configuration.
2025-07-30 05:26:47.696 12838-12943 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Content Card refresh on session created event due to server configuration.
2025-07-30 05:26:47.696 12838-12943 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Feature Flags refresh on session created event due to server configuration.
2025-07-30 05:26:47.696 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='f5d1b92c-f849-411b-a69e-e74d36aecf6a', eventType='SESSION_STARTED'}'
2025-07-30 05:26:47.696 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:26:47.697 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Creating a session seal alarm with a delay of 10000 ms
2025-07-30 05:26:47.699 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_sdk_authentication_enabled'. Using default value 'false'.
2025-07-30 05:26:47.699 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_authentication_enabled' and value: 'false'
2025-07-30 05:26:47.700 12838-12939 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:26:47.701 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_sdk_flavor'. Using default value 'null'.
2025-07-30 05:26:47.701 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_flavor' and value: 'null'
2025-07-30 05:26:47.705 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.i5 fired: bo.app.i5@f97c38b
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.i5 on 1 subscribers.
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .u        org.levimc.launcher                  D  Closed session with id f5d1b92c-f849-411b-a69e-e74d36aecf6a
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-30 05:26:47.706 12838-12943 Braze v24.3.0 .a5       org.levimc.launcher                  V  Not allowing server config info unlock. Returning null.
2025-07-30 05:26:47.708 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:26:47.708 12838-12943 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:47.708 12838-12943 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {}
                                                                                                    }
2025-07-30 05:26:47.709 12838-12943 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Not requesting geofences.
2025-07-30 05:26:47.712 12838-12939 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@9b4cc68
2025-07-30 05:26:47.714 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-30 05:26:47.714 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-30 05:26:47.716 12838-12939 Braze v24.3.0 .l0       org.levimc.launcher                  V  Sending full device due to NOTIFICATIONS_ENABLED true
2025-07-30 05:26:47.716 12838-12939 Braze v24.3.0 .l0       org.levimc.launcher                  V  Remote Notification setting changed to true. Updating user subscription.
2025-07-30 05:26:47.717 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-30 05:26:47.717 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-30 05:26:47.717 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:26:47.718 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@8b2e881
2025-07-30 05:26:47.718 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:26:47.719 12838-12939 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753831607678E9,"session_id":"f5d1b92c-f849-411b-a69e-e74d36aecf6a"} with uid: e4234343-b386-45ec-afd1-529fd732380c
2025-07-30 05:26:47.719 12838-12939 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":19},"time":1.753831607665E9,"session_id":"f120af68-cc62-47fc-9f15-085d8993ceab"} with uid: 1cfb8541-2e29-4ee0-85db-34159a6a4790
2025-07-30 05:26:47.719 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_internal_sdk_metadata'. Using default value '[]'.
2025-07-30 05:26:47.719 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_internal_sdk_metadata' and value: '[]'
2025-07-30 05:26:47.722 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_sdk_metadata'. Using default value '[]'.
2025-07-30 05:26:47.722 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:26:47.722 12838-12939 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:26:47.723 12838-12939 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:26:47.723 12838-12935 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:26:47.724 12838-12935 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=292404692425351(auto) mPendingTransactions.size=0 graphicBufferId=55138790146087 transform=7
2025-07-30 05:26:47.725 12838-12939 Braze v24.3.0 .l0       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:26:47.725 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:26:47.726 12838-12939 Braze v24.3.0 .t0       org.levimc.launcher                  D  Short circuiting execution of network request and immediately marking it as succeeded.
2025-07-30 05:26:47.726 12838-12939 Braze v24.3.0 .j0       org.levimc.launcher                  D  DataSyncRequest executed successfully.
2025-07-30 05:26:47.726 12838-12939 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:47.727 12838-12939 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:26:47.739 12838-12942 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "8f0fecd1e20abeee"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "X-Braze-TriggersRequest" => "true"
                                                                                                    "X-Braze-DataRequest" => "true"
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:26:47.780 12838-12942 Braze v24.3.0 .q5       org.levimc.launcher                  V  Enabling SSL protocols: [TLSv1.2, TLSv1.3]
2025-07-30 05:26:47.840 12838-12844 levimc.launcher         org.levimc.launcher                  W  Cleared Reference was only reachable from finalizer (only reported once)
2025-07-30 05:26:47.862 12838-12845 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=232
2025-07-30 05:26:47.862 12838-12845 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=159
2025-07-30 05:26:47.864 12838-12845 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=162
2025-07-30 05:26:47.865 12838-12846 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 
2025-07-30 05:26:47.871 12838-12846 System                  org.levimc.launcher                  W  A resource failed to call close. 
2025-07-30 05:26:48.129 12838-12942 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 8f0fecd1e20abeee time = 390ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:26:48.130 12838-12942 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:26:48.130 12838-12942 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:26:48.130 12838-12942 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:26:48.131 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:26:48.132 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:26:48.132 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:26:48.133 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:26:48.134 12838-12942 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:26:48.134 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:48.134 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:26:48.135 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:48.135 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:26:48.136 12838-12942 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@9b4cc68
2025-07-30 05:26:48.136 12838-12942 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@8b2e881
2025-07-30 05:26:48.136 12838-12942 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:26:48.137 12838-12942 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:26:48.137 12838-12942 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:26:48.137 12838-12940 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid e4234343-b386-45ec-afd1-529fd732380c
2025-07-30 05:26:48.137 12838-12940 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 1cfb8541-2e29-4ee0-85db-34159a6a4790
2025-07-30 05:26:48.137 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  D  Trigger dispatch completed. Alerting subscribers.
2025-07-30 05:26:48.138 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.x5 fired: TriggerDispatchCompletedEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831607,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753831607678E9,
                                                                                                          "session_id": "f5d1b92c-f849-411b-a69e-e74d36aecf6a"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 19
                                                                                                          },
                                                                                                          "time": 1.753831607665E9,
                                                                                                          "session_id": "f120af68-cc62-47fc-9f15-085d8993ceab"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:48.138 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.x5 on 1 subscribers.
2025-07-30 05:26:48.138 12838-12942 Braze v24.....bo.app.d6 org.levimc.launcher                  D  In flight trigger requests is empty. Executing any pending trigger events.
2025-07-30 05:26:48.588 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:48.588 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"8a991c55-7953-49b3-9d0d-a5860a37ce47","versionCode":"972109401"}}
2025-07-30 05:26:48.589 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:48.589 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:48.589 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:48.589 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:48.589 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:48.589 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:48.590 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp 1 more time(s) after 1000 ms
2025-07-30 05:26:48.683 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:26:48.683 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:26:48.684 12838-12942 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:26:48.684 12838-12939 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:26:48.685 12838-12939 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@9b4cc68
2025-07-30 05:26:48.686 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@8b2e881
2025-07-30 05:26:48.686 12838-12939 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:26:48.693 12838-12942 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "41f0714dc6c35634"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831608,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:26:49.065 12838-12942 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 41f0714dc6c35634 time = 371ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:26:49.065 12838-12942 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:26:49.065 12838-12942 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:26:49.065 12838-12942 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:26:49.065 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831608,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:26:49.065 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831608,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:26:49.066 12838-12942 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:26:49.067 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831608,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:49.067 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:26:49.067 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753831608,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18163559.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:26:49.067 12838-12942 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:26:49.068 12838-12942 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@9b4cc68
2025-07-30 05:26:49.068 12838-12942 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@8b2e881
2025-07-30 05:26:49.068 12838-12942 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:26:49.068 12838-12942 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:26:49.590 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:49.591 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"8a991c55-7953-49b3-9d0d-a5860a37ce47","versionCode":"972109401"}}
2025-07-30 05:26:49.591 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:49.591 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:49.591 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:49.592 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:49.592 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:49.592 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:49.592 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp 0 more time(s) after 1000 ms
2025-07-30 05:26:50.385 12838-12922 fmod                    org.levimc.launcher                  I  AudioDevice::init : Min buffer size: 8224 bytes
2025-07-30 05:26:50.385 12838-12922 fmod                    org.levimc.launcher                  I  AudioDevice::init : Actual buffer size: 8224 bytes
2025-07-30 05:26:50.415 12838-12922 AudioTrack              org.levimc.launcher                  W  Use of stream types is deprecated for operations other than volume control
2025-07-30 05:26:50.415 12838-12922 AudioTrack              org.levimc.launcher                  W  See the documentation of AudioTrack() for what to use instead with android.media.AudioAttributes to qualify your playback use case
2025-07-30 05:26:50.593 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:50.593 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"8a991c55-7953-49b3-9d0d-a5860a37ce47","versionCode":"972109401"}}
2025-07-30 05:26:50.593 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:50.594 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:50.594 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:50.594 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:50.594 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:50.594 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:50.594 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; dropping event /data/user/0/org.levimc.launcher/crash/8a991c55-7953-49b3-9d0d-a5860a37ce47.dmp
2025-07-30 05:26:50.606 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp
2025-07-30 05:26:50.606 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee","versionCode":"972109401"}}
2025-07-30 05:26:50.606 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp
2025-07-30 05:26:50.606 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:50.607 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:50.607 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:50.607 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:50.608 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:50.608 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp 2 more time(s) after 1000 ms
2025-07-30 05:26:51.006 12838-12838 XALJAVA                 org.levimc.launcher                  V  [PresenceManager] XalLogger created.
2025-07-30 05:26:51.016 12838-12838 XALJAVA                 org.levimc.launcher                  W  [P][PresenceManager] Ignoring resume, not currently paused
2025-07-30 05:26:51.019 12838-12838 HttpCallStaticGlue      org.levimc.launcher                  D  Successfully registerered HttpCall methods
2025-07-30 05:26:51.019 12838-12838 XboxLiveAppConfig       org.levimc.launcher                  D  Successfully registerered XboxLiveAppConfig methods
2025-07-30 05:26:51.019 12838-12838 XSAPI.Android           org.levimc.launcher                  D  Successfully registerered HttpCall tcuiMethods
2025-07-30 05:26:51.021 12838-12838 Interop                 org.levimc.launcher                  I  locale is: en_GB
2025-07-30 05:26:51.041 12838-12922 BillingClient           org.levimc.launcher                  W  Unable to retrieve metadata value for enableBillingOverridesTesting.
                                                                                                    java.lang.NullPointerException: Attempt to invoke virtual method 'boolean android.os.Bundle.getBoolean(java.lang.String, boolean)' on a null object reference
                                                                                                    	at com.android.billingclient.api.BillingClient$Builder.zza(com.android.billingclient:billing@@7.1.1:3)
                                                                                                    	at com.android.billingclient.api.BillingClient$Builder.build(com.android.billingclient:billing@@7.1.1:4)
                                                                                                    	at com.mojang.minecraftpe.store.googleplay.GooglePlayBillingImpl.initialize(GooglePlayBillingImpl.java:78)
                                                                                                    	at com.mojang.minecraftpe.store.googleplay.GooglePlayBillingImpl.<init>(GooglePlayBillingImpl.java:56)
                                                                                                    	at com.mojang.minecraftpe.store.googleplay.GooglePlayStore.<init>(GooglePlayStore.java:124)
                                                                                                    	at com.mojang.minecraftpe.store.StoreFactory.createGooglePlayStore(StoreFactory.java:9)
2025-07-30 05:26:51.075 12838-12929 levimc.launcher         org.levimc.launcher                  A  java_vm_ext.cc:616] JNI DETECTED ERROR IN APPLICATION: JNI GetStringUTFChars called with pending exception java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process org.levimc.launcher. Make sure to call FirebaseApp.initializeApp(Context) first.
                                                                                                    java_vm_ext.cc:616]   at com.google.firebase.FirebaseApp com.google.firebase.FirebaseApp.getInstance() (FirebaseApp.java:184)
                                                                                                    java_vm_ext.cc:616]   at com.google.firebase.messaging.FirebaseMessaging com.google.firebase.messaging.FirebaseMessaging.getInstance() (FirebaseMessaging.java:135)
                                                                                                    java_vm_ext.cc:616]   at void com.mojang.minecraftpe.NotificationListenerService.retrieveDeviceToken() (NotificationListenerService.java:71)
                                                                                                    java_vm_ext.cc:616]   at java.lang.String com.mojang.minecraftpe.NotificationListenerService.getDeviceRegistrationToken() (NotificationListenerService.java:60)
                                                                                                    java_vm_ext.cc:616] 
                                                                                                    java_vm_ext.cc:616]     in call to GetStringUTFChars
2025-07-30 05:26:51.101 12838-12922 LicenseChecker          org.levimc.launcher                  I  Binding to licensing service.
2025-07-30 05:26:51.105 12838-12922 MinecraftL...erCallback org.levimc.launcher                  I  error: 6
2025-07-30 05:26:51.170 12838-12994 BackendRegistry         org.levimc.launcher                  W  Application info not found.
2025-07-30 05:26:51.170 12838-12994 BackendRegistry         org.levimc.launcher                  W  Could not retrieve metadata, returning empty list of transport backends.
2025-07-30 05:26:51.170 12838-12994 TransportRuntime        org.levimc.launcher                  W  Transport backend 'cct' is not registered
2025-07-30 05:26:51.287 12838-12922 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Async Services Manager starting in Legacy mode
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709] Runtime aborting...
                                                                                                    runtime.cc:709] Dumping all threads without mutator lock held
                                                                                                    runtime.cc:709] All threads:
                                                                                                    runtime.cc:709] DALVIK THREADS (57):
                                                                                                    runtime.cc:709] "main" prio=10 tid=1 Native
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x72060a58 self=0xb400007add521c00
                                                                                                    runtime.cc:709]   | sysTid=12838 nice=-10 cgrp=top-app sched=0/0 handle=0x7adf10ad28
                                                                                                    runtime.cc:709]   | state=S schedstat=( 2029747471 195827005 2048 ) utm=162 stm=40 core=1 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7fcaa83000-0x7fcaa85000 stackSize=8188KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf45c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 00201230  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::WaitHoldingLocks+136) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 005ee448  /apex/com.android.art/lib64/libart.so (artJniMethodEnd+120) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 0068f48c  /apex/com.android.art/lib64/libart.so (art_jni_method_end+12) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #04 pc 023e4ad8  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+280)
                                                                                                    runtime.cc:709]   native: #05 pc 0205b7cc  /memfd:jit-cache (deleted) (offset 2000000) (android.os.MessageQueue.next+332)
                                                                                                    runtime.cc:709]   native: #06 pc 0205abd4  /memfd:jit-cache (deleted) (offset 2000000) (android.os.Looper.loopOnce+164)
                                                                                                    runtime.cc:709]   native: #07 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 00296264  /system/framework/framework.jar (offset 1225000) (android.os.Looper.loop)
                                                                                                    runtime.cc:709]   native: #12 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 001d4df4  /system/framework/framework.jar (offset b000) (android.app.ActivityThread.main)
                                                                                                    runtime.cc:709]   native: #17 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 003273d0  /apex/com.android.art/lib64/libart.so (_jobject* art::InvokeMethod<8>+544) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #22 pc 005c6f80  /apex/com.android.art/lib64/libart.so (art::Method_invoke +32) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #23 pc 00344500  /apex/com.android.art/lib64/libart.so (art_quick_generic_jni_trampoline+144) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 00349870  /system/framework/framework.jar (offset 246a000) (com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run)
                                                                                                    runtime.cc:709]   native: #29 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #30 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #31 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   at android.os.MessageQueue.nativePollOnce(Native method)
                                                                                                    runtime.cc:709]   at android.os.MessageQueue.next(MessageQueue.java:346)
                                                                                                    runtime.cc:709]   at android.os.Looper.loopOnce(Looper.java:214)
                                                                                                    runtime.cc:709]   at android.os.Looper.loop(Looper.java:342)
                                                                                                    runtime.cc:709]   at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    runtime.cc:709]   at java.lang.reflect.Method.invoke(Native method)
                                                                                                    runtime.cc:709]   at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    runtime.cc:709]   at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "ReferenceQueueDaemon" prio=5 tid=7 Waiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x2000608 self=0xb4000079e590ec00
                                                                                                    runtime.cc:709]   | sysTid=12845 nice=4 cgrp=top-app sched=0/0 handle=0x79ef0645f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 5697844 1104769 13 ) utm=0 stm=0 core=7 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x79eec61000-0x79eec63000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf45c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 00201230  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::WaitHoldingLocks+136) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b3650  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+1088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023e1924  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+116)
                                                                                                    runtime.cc:709]   native: #04 pc 0200691c  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Object.wait+124)
                                                                                                    runtime.cc:709]   native: #05 pc 020067f4  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Object.wait+116)
                                                                                                    runtime.cc:709]   native: #06 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #09 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 00030c30  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$ReferenceQueueDaemon.runInternal)
                                                                                                    runtime.cc:709]   native: #11 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0002fd44  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$Daemon.run)
                                                                                                    runtime.cc:709]   native: #16 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 0011a184  /apex/com.android.art/javalib/core-oj.jar (java.lang.Thread.run)
                                                                                                    runtime.cc:709]   native: #21 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #22 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #23 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #29 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Native method)
                                                                                                    runtime.cc:709]   - waiting on <0x0a01734b> (a java.lang.Class<java.lang.ref.ReferenceQueue>)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Object.java:405)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Object.java:543)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$ReferenceQueueDaemon.runInternal(Daemons.java:260)
                                                                                                    runtime.cc:709]   - locked <0x0a01734b> (a java.lang.Class<java.lang.ref.ReferenceQueue>)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$Daemon.run(Daemons.java:132)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "FinalizerWatchdogDaemon" prio=5 tid=8 Sleeping
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x2000728 self=0xb4000079e5912400
                                                                                                    runtime.cc:709]   | sysTid=12847 nice=4 cgrp=top-app sched=0/0 handle=0x798f2115f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 684692 424078 6 ) utm=0 stm=0 core=5 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x798ee0e000-0x798ee10000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0020a20c  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::TimedWait+296) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b34a8  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+664) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 0271ba94  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+132)
                                                                                                    runtime.cc:709]   native: #04 pc 0271a284  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.sleep+580)
                                                                                                    runtime.cc:709]   native: #05 pc 02719f90  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.sleep+112)
                                                                                                    runtime.cc:709]   native: #06 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 00030228  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$FinalizerWatchdogDaemon.sleepForNanos)
                                                                                                    runtime.cc:709]   native: #11 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 00030508  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$FinalizerWatchdogDaemon.waitForProgress)
                                                                                                    runtime.cc:709]   native: #16 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 000309cc  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$FinalizerWatchdogDaemon.runInternal)
                                                                                                    runtime.cc:709]   native: #21 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #22 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #23 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 0002fd44  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$Daemon.run)
                                                                                                    runtime.cc:709]   native: #26 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #29 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #30 pc 0011a184  /apex/com.android.art/javalib/core-oj.jar (java.lang.Thread.run)
                                                                                                    runtime.cc:709]   native: #31 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #32 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #33 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #34 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #35 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #36 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #37 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #38 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #39 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Native method)
                                                                                                    runtime.cc:709]   - sleeping on <0x09457e41> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Thread.java:451)
                                                                                                    runtime.cc:709]   - locked <0x09457e41> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Thread.java:356)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$FinalizerWatchdogDaemon.sleepForNanos(Daemons.java:534)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$FinalizerWatchdogDaemon.waitForProgress(Daemons.java:593)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$FinalizerWatchdogDaemon.runInternal(Daemons.java:465)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$Daemon.run(Daemons.java:132)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "FinalizerDaemon" prio=5 tid=9 Waiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x2000698 self=0xb4000079e5910800
                                                                                                    runtime.cc:709]   | sysTid=12846 nice=4 cgrp=top-app sched=0/0 handle=0x799061b5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 8266923 1155076 16 ) utm=0 stm=0 core=7 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7990218000-0x799021a000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf45c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #01 pc 00201230  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::WaitHoldingLocks+136) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b3650  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+1088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023e1924  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+116)
                                                                                                    runtime.cc:709]   native: #04 pc 0200691c  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Object.wait+124)
                                                                                                    runtime.cc:709]   native: #05 pc 0200affc  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.ref.ReferenceQueue.remove+348)
                                                                                                    runtime.cc:709]   native: #06 pc 0200ade4  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.ref.ReferenceQueue.remove+116)
                                                                                                    runtime.cc:709]   native: #07 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 00030114  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$FinalizerDaemon.runInternal)
                                                                                                    runtime.cc:709]   native: #12 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 0002fd44  /apex/com.android.art/javalib/core-libart.jar (java.lang.Daemons$Daemon.run)
                                                                                                    runtime.cc:709]   native: #17 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 0011a184  /apex/com.android.art/javalib/core-oj.jar (java.lang.Thread.run)
                                                                                                    runtime.cc:709]   native: #22 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #23 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #27 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #29 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #30 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Native method)
                                                                                                    runtime.cc:709]   - waiting on <0x0654bfe6> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Object.java:405)
                                                                                                    runtime.cc:709]   at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:207)
                                                                                                    runtime.cc:709]   - locked <0x0654bfe6> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:228)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$FinalizerDaemon.runInternal(Daemons.java:348)
                                                                                                    runtime.cc:709]   at java.lang.Daemons$Daemon.run(Daemons.java:132)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp api.github.com" prio=5 tid=28 Native
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x22a6340 self=0xb4000079882a8000
                                                                                                    runtime.cc:709]   | sysTid=12907 nice=0 cgrp=top-app sched=0/0 handle=0x78e2f215f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 14790005 1117845 12 ) utm=1 stm=0 core=1 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78e2b1e000-0x78e2b20000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 00102b64  /apex/com.android.runtime/lib64/bionic/libc.so (recvfrom+4) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0002c358  /apex/com.android.art/lib64/libopenjdk.so (NET_Read+80) (BuildId: fa8f19868b7278513a6b7a261dd347a0)
                                                                                                    runtime.cc:709]   native: #02 pc 0002cebc  /apex/com.android.art/lib64/libopenjdk.so (SocketInputStream_socketRead0+216) (BuildId: fa8f19868b7278513a6b7a261dd347a0)
                                                                                                    runtime.cc:709]   native: #03 pc 025c1028  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+168)
                                                                                                    runtime.cc:709]   native: #04 pc 0251cc5c  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.socketRead+140)
                                                                                                    runtime.cc:709]   native: #05 pc 0251c4cc  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.read+412)
                                                                                                    runtime.cc:709]   native: #06 pc 0251c154  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.read+180)
                                                                                                    runtime.cc:709]   native: #07 pc 0068a120  /apex/com.android.art/lib64/libart.so (nterp_helper+4016) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 0001c39e  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket+50)
                                                                                                    runtime.cc:709]   native: #09 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 0001c216  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket+350)
                                                                                                    runtime.cc:709]   native: #11 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 0001c3e2  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable+2)
                                                                                                    runtime.cc:709]   native: #13 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 0001c33c  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read+24)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #15 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 000637f8  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okio.InputStreamSource.read)
                                                                                                    runtime.cc:709]   native: #20 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #22 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #23 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 00058548  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okio.AsyncTimeout$source$1.read)
                                                                                                    runtime.cc:709]   native: #25 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #29 pc 000694f4  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okio.RealBufferedSource.request)
                                                                                                    runtime.cc:709]   native: #30 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #31 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #32 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #33 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #34 pc 0006a704  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okio.RealBufferedSource.require)
                                                                                                    runtime.cc:709]   native: #35 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #36 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #37 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #38 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #39 pc 00049cb0  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http2.Http2Reader.nextFrame)
                                                                                                    runtime.cc:709]   native: #40 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #41 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #42 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #43 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #44 pc 000473b0  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke)
                                                                                                    runtime.cc:709]   native: #45 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #46 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #47 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #48 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #49 pc 00046dec  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke)
                                                                                                    runtime.cc:709]   native: #50 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #51 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #52 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #53 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #54 pc 0003a3dc  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce)
                                                                                                    runtime.cc:709]   native: #55 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #56 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #57 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #58 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #59 pc 0003b7b0  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner.runTask)
                                                                                                    runtime.cc:709]   native: #60 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #61 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #62 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #63 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #64 pc 0003b42c  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner.access$runTask)
                                                                                                    runtime.cc:709]   native: #65 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #66 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #67 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #68 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #69 pc 0003aea8  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner$runnable$1.run)
                                                                                                    runtime.cc:709]   native: #70 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #71 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #72 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #73 pc 0219e6bc  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+444)
                                                                                                    runtime.cc:709]   native: #74 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #75 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #76 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #77 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #78 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #79 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #80 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #81 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.socketRead0(Native method)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
                                                                                                    runtime.cc:709]   - locked <@addr=0x2281be8> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at okio.InputStreamSource.read(JvmOkio.kt:93)
                                                                                                    runtime.cc:709]   at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
                                                                                                    runtime.cc:709]   at okio.RealBufferedSource.request(RealBufferedSource.kt:209)
                                                                                                    runtime.cc:709]   at okio.RealBufferedSource.require(RealBufferedSource.kt:202)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp TaskRunner" prio=5 tid=30 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x22acb28 self=0xb4000078f981dc00
                                                                                                    runtime.cc:709]   | sysTid=12909 nice=0 cgrp=top-app sched=0/0 handle=0x78e070d5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 3430156 0 8 ) utm=0 stm=0 core=2 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78e030a000-0x78e030c000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0020a20c  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::TimedWait+296) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b34a8  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+664) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023e1924  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+116)
                                                                                                    runtime.cc:709]   native: #04 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #05 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #08 pc 0003adf8  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner$RealBackend.coordinatorWait)
                                                                                                    runtime.cc:709]   native: #09 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 0003b0e0  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner.awaitTaskToRun)
                                                                                                    runtime.cc:709]   native: #14 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 0003aea8  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.concurrent.TaskRunner$runnable$1.run)
                                                                                                    runtime.cc:709]   native: #19 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #22 pc 0219e6bc  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+444)
                                                                                                    runtime.cc:709]   native: #23 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #24 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #25 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #29 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #30 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Native method)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   - waiting on <0x012abd7d> (a okhttp3.internal.concurrent.TaskRunner)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner$RealBackend.coordinatorWait(TaskRunner.kt:294)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner.awaitTaskToRun(TaskRunner.kt:218)
                                                                                                    runtime.cc:709]   at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:59)
                                                                                                    runtime.cc:709]   - locked <0x012abd7d> (a okhttp3.internal.concurrent.TaskRunner)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "Thread-15" prio=5 tid=45 Sleeping
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x241ecd0 self=0xb4000078a7160800
                                                                                                    runtime.cc:709]   | sysTid=12949 nice=0 cgrp=top-app sched=0/0 handle=0x78891f75f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 20642158 8232765 67 ) utm=1 stm=0 core=5 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7889100000-0x7889102000 stackSize=989KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0020a20c  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::TimedWait+296) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b34a8  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+664) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 0271ba94  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+132)
                                                                                                    runtime.cc:709]   native: #04 pc 0271a284  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.sleep+580)
                                                                                                    runtime.cc:709]   native: #05 pc 02719f90  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.sleep+112)
                                                                                                    runtime.cc:709]   native: #06 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 001b621c  /data/data/org.levimc.launcher/code_cache/dex/classes2.dex (com.mojang.minecraftpe.CrashManager.uploadCrashFile)
                                                                                                    runtime.cc:709]   native: #11 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0032b228  /apex/com.android.art/lib64/libart.so (art::JValue art::InvokeVirtualOrInterfaceWithVarArgs<_jmethodID*>+924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 00666d74  /apex/com.android.art/lib64/libart.so (art::JNI<true>::CallObjectMethodV+116) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 003552d4  /apex/com.android.art/lib64/libart.so (art::::CheckJNI::CallMethodV +548) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #18 pc 005e7940  /apex/com.android.art/lib64/libart.so (art::::CheckJNI::CallObjectMethodV +72) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 05c227c8  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #20 pc 05c2e778  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #21 pc 05bc32dc  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #22 pc 05bda708  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #23 pc 0e59ea74  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #24 pc 0e59e690  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #25 pc 0e5c3a4c  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #26 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #27 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Native method)
                                                                                                    runtime.cc:709]   - sleeping on <0x009cf472> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Thread.java:451)
                                                                                                    runtime.cc:709]   - locked <0x009cf472> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at java.lang.Thread.sleep(Thread.java:356)
                                                                                                    runtime.cc:709]   at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:252)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp ConnectionPool" prio=5 tid=49 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x2435da0 self=0xb4000078da3e3c00
                                                                                                    runtime.cc:709]   | sysTid=12962 nice=0 cgrp=top-app sched=0/0 handle=0x788060d5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 703539 1512539 10 ) utm=0 stm=0 core=3 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x788020a000-0x788020c000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0020a20c  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::TimedWait+296) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b34a8  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+664) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023e1924  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+116)
                                                                                                    runtime.cc:709]   native: #04 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #05 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #07 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 00014724  /apex/com.android.art/javalib/okhttp.jar (com.android.okhttp.ConnectionPool$1.run)
                                                                                                    runtime.cc:709]   native: #09 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 0219e6bc  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+444)
                                                                                                    runtime.cc:709]   native: #13 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #14 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #15 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #20 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Native method)
                                                                                                    runtime.cc:709]   - waiting on <0x077b00c3> (a com.android.okhttp.ConnectionPool)
                                                                                                    runtime.cc:709]   at com.android.okhttp.ConnectionPool$1.run(ConnectionPool.java:106)
                                                                                                    runtime.cc:709]   - locked <0x077b00c3> (a com.android.okhttp.ConnectionPool)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "Okio Watchdog" prio=5 tid=50 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x24433e8 self=0xb4000078da3e5800
                                                                                                    runtime.cc:709]   | sysTid=12963 nice=0 cgrp=top-app sched=0/0 handle=0x7a19c475f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 659460 1489155 8 ) utm=0 stm=0 core=0 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7a19844000-0x7a19846000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 0020a20c  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::TimedWait+296) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 004b34a8  /apex/com.android.art/lib64/libart.so (art::Monitor::Wait+664) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023e1924  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+116)
                                                                                                    runtime.cc:709]   native: #04 pc 0278cf18  /memfd:jit-cache (deleted) (offset 2000000) (com.android.okhttp.okio.AsyncTimeout.awaitTimeout+392)
                                                                                                    runtime.cc:709]   native: #05 pc 0278ccd8  /memfd:jit-cache (deleted) (offset 2000000) (com.android.okhttp.okio.AsyncTimeout.access$000+104)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #06 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 000332d8  /apex/com.android.art/javalib/okhttp.jar (com.android.okhttp.okio.AsyncTimeout$Watchdog.run)
                                                                                                    runtime.cc:709]   native: #11 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #19 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at java.lang.Object.wait(Native method)
                                                                                                    runtime.cc:709]   - waiting on <0x036ced40> (a java.lang.Class<com.android.okhttp.okio.AsyncTimeout>)
                                                                                                    runtime.cc:709]   at com.android.okhttp.okio.AsyncTimeout.awaitTimeout(AsyncTimeout.java:325)
                                                                                                    runtime.cc:709]   - locked <0x036ced40> (a java.lang.Class<com.android.okhttp.okio.AsyncTimeout>)
                                                                                                    runtime.cc:709]   at com.android.okhttp.okio.AsyncTimeout.access$000(AsyncTimeout.java:42)
                                                                                                    runtime.cc:709]   at com.android.okhttp.okio.AsyncTimeout$Watchdog.run(AsyncTimeout.java:288)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp https://self.events.data.microsoft.com/..." prio=5 tid=55 Native
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x251e6e0 self=0xb40000788d6b6800
                                                                                                    runtime.cc:709]   | sysTid=12989 nice=0 cgrp=top-app sched=0/0 handle=0x7874dff5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 26923387 1755154 11 ) utm=2 stm=0 core=1 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78749fc000-0x78749fe000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 001030e8  /apex/com.android.runtime/lib64/bionic/libc.so (__ppoll+8) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 000a698c  /apex/com.android.runtime/lib64/bionic/libc.so (poll+92) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #02 pc 0002ca5c  /apex/com.android.art/lib64/libopenjdk.so (NET_Timeout+184) (BuildId: fa8f19868b7278513a6b7a261dd347a0)
                                                                                                    runtime.cc:709]   native: #03 pc 0002cea4  /apex/com.android.art/lib64/libopenjdk.so (SocketInputStream_socketRead0+192) (BuildId: fa8f19868b7278513a6b7a261dd347a0)
                                                                                                    runtime.cc:709]   native: #04 pc 025c1028  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+168)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #05 pc 0251cc5c  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.socketRead+140)
                                                                                                    runtime.cc:709]   native: #06 pc 0251c4cc  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.read+412)
                                                                                                    runtime.cc:709]   native: #07 pc 0251c154  /memfd:jit-cache (deleted) (offset 2000000) (java.net.SocketInputStream.read+180)
                                                                                                    runtime.cc:709]   native: #08 pc 0068a120  /apex/com.android.art/lib64/libart.so (nterp_helper+4016) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 0001c39e  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket+50)
                                                                                                    runtime.cc:709]   native: #10 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 0001c216  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket+350)
                                                                                                    runtime.cc:709]   native: #12 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 0001c054  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket)
                                                                                                    runtime.cc:709]   native: #14 pc 006891a4  /apex/com.android.art/lib64/libart.so (nterp_helper+52) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0001d3e0  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket.doHandshake+108)
                                                                                                    runtime.cc:709]   native: #16 pc 0068a0c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 0001d76a  /apex/com.android.conscrypt/javalib/conscrypt.jar (com.android.org.conscrypt.ConscryptEngineSocket.startHandshake+58)
                                                                                                    runtime.cc:709]   native: #18 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #22 pc 0003fe3c  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealConnection.connectTls)
                                                                                                    runtime.cc:709]   native: #23 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #26 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #27 pc 00040254  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealConnection.establishProtocol)
                                                                                                    runtime.cc:709]   native: #28 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #29 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #30 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #31 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #32 pc 0003f8dc  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealConnection.connect)
                                                                                                    runtime.cc:709]   native: #33 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #34 pc 00570140  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<true>+1796) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #35 pc 005cc0ec  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+11904) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #36 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #37 pc 0003c288  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.ExchangeFinder.findConnection)
                                                                                                    runtime.cc:709]   native: #38 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #39 pc 00570140  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<true>+1796) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #40 pc 005cc0ec  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+11904) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #41 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #42 pc 0003c630  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.ExchangeFinder.findHealthyConnection)
                                                                                                    runtime.cc:709]   native: #43 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #44 pc 00570140  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<true>+1796) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #45 pc 005cc0ec  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+11904) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #46 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #47 pc 0003c6b4  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.ExchangeFinder.find)
                                                                                                    runtime.cc:709]   native: #48 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #49 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #50 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #51 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #52 pc 0003dc1c  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealCall.initExchange$okhttp)
                                                                                                    runtime.cc:709]   native: #53 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #54 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #55 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #56 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #57 pc 0003ba20  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.ConnectInterceptor.intercept)
                                                                                                    runtime.cc:709]   native: #58 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #59 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #60 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #61 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #62 pc 00042360  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.RealInterceptorChain.proceed)
                                                                                                    runtime.cc:709]   native: #63 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #64 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #65 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #66 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #67 pc 00035e08  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.cache.CacheInterceptor.intercept)
                                                                                                    runtime.cc:709]   native: #68 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #69 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #70 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #71 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #72 pc 00042360  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.RealInterceptorChain.proceed)
                                                                                                    runtime.cc:709]   native: #73 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #74 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #75 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #76 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #77 pc 00040f7c  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.BridgeInterceptor.intercept)
                                                                                                    runtime.cc:709]   native: #78 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #79 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #80 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #81 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #82 pc 00042360  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.RealInterceptorChain.proceed)
                                                                                                    runtime.cc:709]   native: #83 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #84 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #85 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #86 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #87 pc 00042e60  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept)
                                                                                                    runtime.cc:709]   native: #88 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #89 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #90 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #91 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #92 pc 00042360  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.http.RealInterceptorChain.proceed)
                                                                                                    runtime.cc:709]   native: #93 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #94 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #95 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #96 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #97 pc 0003da40  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp)
                                                                                                    runtime.cc:709]   native: #98 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #99 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #100 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #101 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #102 pc 0003d018  [anon:dalvik-classes10.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (okhttp3.internal.connection.RealCall$AsyncCall.run)
                                                                                                    runtime.cc:709]   native: #103 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #104 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #105 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #106 pc 0219e6bc  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+444)
                                                                                                    runtime.cc:709]   native: #107 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #108 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #109 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #110 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #111 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #112 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #113 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #114 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   at java.net.SocketInputStream.socketRead0(Native method)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    runtime.cc:709]   at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(unavailable:0)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:242)
                                                                                                    runtime.cc:709]   at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:224)
                                                                                                    runtime.cc:709]   - locked <@addr=0x2526948> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    runtime.cc:709]   at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
                                                                                                    runtime.cc:709]   at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
                                                                                                    runtime.cc:709]   at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "pool-8-thread-1" prio=5 tid=57 Runnable
                                                                                                    runtime.cc:709]   | group="" sCount=0 ucsCount=0 flags=0 obj=0x2592670 self=0xb4000078d96ea000
                                                                                                    runtime.cc:709]   | sysTid=12994 nice=0 cgrp=top-app sched=0/0 handle=0x78739f55f0
                                                                                                    runtime.cc:709]   | state=R schedstat=( 8861844 606461 13 ) utm=0 stm=0 core=3 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78735f2000-0x78735f4000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes= "mutator lock"(shared held)
                                                                                                    runtime.cc:709]   native: #00 pc 004ca2ec  /apex/com.android.art/lib64/libart.so (art::DumpNativeStack+108) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #01 pc 004342b4  /apex/com.android.art/lib64/libart.so (art::Thread::DumpStack const+436) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00433fd0  /apex/com.android.art/lib64/libart.so (art::DumpCheckpoint::Run+120) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #03 pc 0028e190  /apex/com.android.art/lib64/libart.so (art::Thread::RunCheckpointFunction+144) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #04 pc 003580ac  /apex/com.android.art/lib64/libart.so (art::::CheckJNI::ReleaseStringCharsInternal +824) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #05 pc 00159244  /system/lib64/libandroid_runtime.so (android::nativePrepareStatement+148) (BuildId: 407312aeb216060d41c1d46e91beb8de)
                                                                                                    runtime.cc:709]   native: #06 pc 00344500  /apex/com.android.art/lib64/libart.so (art_quick_generic_jni_trampoline+144) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 00446e24  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.-$$Nest$smnativePrepareStatement)
                                                                                                    runtime.cc:709]   native: #12 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 00442918  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection$PreparedStatementCache.createStatement)
                                                                                                    runtime.cc:709]   native: #17 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 00445e94  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.acquirePreparedStatementLI)
                                                                                                    runtime.cc:709]   native: #22 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #23 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #24 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #25 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #26 pc 00445e78  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.acquirePreparedStatement)
                                                                                                    runtime.cc:709]   native: #27 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #28 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #29 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #30 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #31 pc 00446be8  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.executeForString)
                                                                                                    runtime.cc:709]   native: #32 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #33 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #34 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #35 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #36 pc 00449158  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.setJournalMode)
                                                                                                    runtime.cc:709]   native: #37 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #38 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #39 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #40 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #41 pc 00449100  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.setJournalFromConfiguration)
                                                                                                    runtime.cc:709]   native: #42 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #43 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #44 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #45 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #46 pc 00447f60  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.open)
                                                                                                    runtime.cc:709]   native: #47 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #48 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #49 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #50 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #51 pc 00445d68  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnection.open)
                                                                                                    runtime.cc:709]   native: #52 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #53 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #54 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #55 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #56 pc 0044347c  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnectionPool.openConnectionLocked)
                                                                                                    runtime.cc:709]   native: #57 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #58 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #59 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #60 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #61 pc 00445424  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnectionPool.open)
                                                                                                    runtime.cc:709]   native: #62 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #63 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #64 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #65 pc 0068a120  /apex/com.android.art/lib64/libart.so (nterp_helper+4016) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #66 pc 004439c2  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteConnectionPool.open+18)
                                                                                                    runtime.cc:709]   native: #67 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #68 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #69 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #70 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #71 pc 0044e8d8  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteDatabase.openInner)
                                                                                                    runtime.cc:709]   native: #72 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #73 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #74 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #75 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #76 pc 0044e818  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteDatabase.open)
                                                                                                    runtime.cc:709]   native: #77 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #78 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #79 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #80 pc 0068a120  /apex/com.android.art/lib64/libart.so (nterp_helper+4016) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #81 pc 0044b232  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteDatabase.openDatabase+134)
                                                                                                    runtime.cc:709]   native: #82 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #83 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #84 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #85 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #86 pc 00450b34  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked)
                                                                                                    runtime.cc:709]   native: #87 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #88 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #89 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #90 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #91 pc 00450f3c  /system/framework/framework.jar (offset b000) (android.database.sqlite.SQLiteOpenHelper.getWritableDatabase)
                                                                                                    runtime.cc:709]   native: #92 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #93 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #94 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #95 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #96 pc 0033fed0  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.$r8$lambda$pZuvEfO_xLfFaI7wN1aaaWpYHdo)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #97 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #98 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #99 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #100 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #101 pc 0033fb94  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore$$ExternalSyntheticLambda2.produce)
                                                                                                    runtime.cc:709]   native: #102 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #103 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #104 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #105 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #106 pc 00340ec8  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.retryIfDbLocked)
                                                                                                    runtime.cc:709]   native: #107 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #108 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #109 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #110 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #111 pc 0033feec  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.getDb)
                                                                                                    runtime.cc:709]   native: #112 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #113 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #114 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #115 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #116 pc 00340f3c  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.runCriticalSection)
                                                                                                    runtime.cc:709]   native: #117 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #118 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #119 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #120 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #121 pc 0033eb04  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.jobscheduling.WorkInitializer.lambda$ensureContextsScheduled$1$com-google-android-datatransport-runtime-scheduling-jobscheduling-WorkInitializer)
                                                                                                    runtime.cc:709]   native: #122 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #123 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #124 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #125 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #126 pc 0033e970  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.scheduling.jobscheduling.WorkInitializer$$ExternalSyntheticLambda1.run)
                                                                                                    runtime.cc:709]   native: #127 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #128 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #129 pc 005c9d1c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+2736) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #130 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #131 pc 0033971c  /data/data/org.levimc.launcher/code_cache/dex/classes.dex (com.google.android.datatransport.runtime.SafeLoggingExecutor$SafeLoggingRunnable.run)
                                                                                                    runtime.cc:709]   native: #132 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #133 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #134 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #135 pc 0219e6bc  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+444)
                                                                                                    runtime.cc:709]   native: #136 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #137 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #138 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #139 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #140 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #141 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #142 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #143 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.nativePrepareStatement(Native method)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.-$$Nest$smnativePrepareStatement(unavailable:0)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection$PreparedStatementCache.createStatement(SQLiteConnection.java:2012)
                                                                                                    runtime.cc:709]   - locked <@addr=0x2596600> (a android.database.sqlite.SQLiteConnection$PreparedStatementCache)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.acquirePreparedStatementLI(SQLiteConnection.java:1548)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.acquirePreparedStatement(SQLiteConnection.java:1576)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.executeForString(SQLiteConnection.java:1137)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.setJournalMode(SQLiteConnection.java:690)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.setJournalFromConfiguration(SQLiteConnection.java:569)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:425)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:244)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:765)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:299)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:266)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:1679)
                                                                                                    runtime.cc:709]   - locked <@addr=0x25954c8> (a java.lang.Object)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:1616)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:1257)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:476)
                                                                                                    runtime.cc:709]   at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:419)
                                                                                                    runtime.cc:709]   - locked <@addr=0x2586500> (a com.google.android.datatransport.runtime.scheduling.persistence.SchemaManager)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.$r8$lambda$pZuvEfO_xLfFaI7wN1aaaWpYHdo(unavailable:0)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore$$ExternalSyntheticLambda2.produce(unavailable:2)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.retryIfDbLocked(SQLiteEventStore.java:582)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.getDb(SQLiteEventStore.java:95)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.persistence.SQLiteEventStore.runCriticalSection(SQLiteEventStore.java:765)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.jobscheduling.WorkInitializer.lambda$ensureContextsScheduled$1$com-google-android-datatransport-runtime-scheduling-jobscheduling-WorkInitializer(WorkInitializer.java:54)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.scheduling.jobscheduling.WorkInitializer$$ExternalSyntheticLambda1.run(unavailable:2)
                                                                                                    runtime.cc:709]   at com.google.android.datatransport.runtime.SafeLoggingExecutor$SafeLoggingRunnable.run(SafeLoggingExecutor.java:47)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #19 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #20 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at android.os.MessageQueue.nativePollOnce(Native method)
                                                                                                    runtime.cc:709]   at android.os.MessageQueue.next(MessageQueue.java:346)
                                                                                                    runtime.cc:709]   at android.os.Looper.loopOnce(Looper.java:214)
                                                                                                    runtime.cc:709]   at android.os.Looper.loop(Looper.java:342)
                                                                                                    runtime.cc:709]   at android.os.HandlerThread.run(HandlerThread.java:85)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "SurfaceSyncGroupTimer" prio=5 tid=17 Native
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x21087c0 self=0xb4000079e591d400
                                                                                                    runtime.cc:709]   | sysTid=12874 nice=0 cgrp=top-app sched=0/0 handle=0x78f4d675f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 1073229 620923 11 ) utm=0 stm=0 core=5 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78f4964000-0x78f4966000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 00102fc8  /apex/com.android.runtime/lib64/bionic/libc.so (__epoll_pwait+8) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 00013004  /system/lib64/libutils.so (android::Looper::pollOnce+212) (BuildId: 57a4403f908f06aef0772903d0e965c3)
                                                                                                    runtime.cc:709]   native: #02 pc 00194c0c  /system/lib64/libandroid_runtime.so (android::android_os_MessageQueue_nativePollOnce+44) (BuildId: 407312aeb216060d41c1d46e91beb8de)
                                                                                                    runtime.cc:709]   native: #03 pc 023e4a4c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 0205b7cc  /memfd:jit-cache (deleted) (offset 2000000) (android.os.MessageQueue.next+332)
                                                                                                    runtime.cc:709]   native: #05 pc 0205abd4  /memfd:jit-cache (deleted) (offset 2000000) (android.os.Looper.loopOnce+164)
                                                                                                    runtime.cc:709]   native: #06 pc 0205987c  /memfd:jit-cache (deleted) (offset 2000000) (android.os.Looper.loop+252)
                                                                                                    runtime.cc:709]   native: #07 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 0025f310  /system/framework/framework.jar (offset 1225000) (android.os.HandlerThread.run)
                                                                                                    runtime.cc:709]   native: #12 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.311 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #06 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 0025a258  /system/framework/framework.jar (offset 1225000) (android.os.FileObserver$ObserverThread.run)
                                                                                                    runtime.cc:709]   native: #08 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #16 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at android.os.FileObserver$ObserverThread.observe(Native method)
                                                                                                    runtime.cc:709]   at android.os.FileObserver$ObserverThread.run(FileObserver.java:116)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp Dispatcher" prio=5 tid=25 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x2273358 self=0xb40000798916b400
                                                                                                    runtime.cc:709]   | sysTid=12897 nice=0 cgrp=top-app sched=0/0 handle=0x78e67355f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 91770998 2108850 28 ) utm=8 stm=0 core=7 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78e6332000-0x78e6334000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005389c4  /apex/com.android.art/lib64/libart.so (art::Thread::Park+672) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 025b7938  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.parkNanos+200)
                                                                                                    runtime.cc:709]   native: #05 pc 025b6e44  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.LinkedTransferQueue$DualNode.await+836)
                                                                                                    runtime.cc:709]   native: #06 pc 02488f80  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue$Transferer.xferLifo+560)
                                                                                                    runtime.cc:709]   native: #07 pc 02488c60  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue.xfer+144)
                                                                                                    runtime.cc:709]   native: #08 pc 025b2e5c  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue.poll+188)
                                                                                                    runtime.cc:709]   native: #09 pc 0219ecf4  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.getTask+484)
                                                                                                    runtime.cc:709]   native: #10 pc 0219e5b8  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+184)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #24 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at jdk.internal.misc.Unsafe.park(Native method)
                                                                                                    runtime.cc:709]   - waiting on an unknown object
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1847)
                                                                                                    runtime.cc:709]   at okio.AsyncTimeout$Companion.awaitTimeout$okio(AsyncTimeout.kt:308)
                                                                                                    runtime.cc:709]   at okio.AsyncTimeout$Watchdog.run(AsyncTimeout.kt:186)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "OkHttp TaskRunner" prio=5 tid=32 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x22b2270 self=0xb4000078f9833400
                                                                                                    runtime.cc:709]   | sysTid=12911 nice=0 cgrp=top-app sched=0/0 handle=0x78ddef95f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 904001 67384 2 ) utm=0 stm=0 core=2 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78ddaf6000-0x78ddaf8000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005389c4  /apex/com.android.art/lib64/libart.so (art::Thread::Park+672) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #05 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 00265368  /apex/com.android.art/javalib/core-oj.jar (java.util.concurrent.locks.LockSupport.parkNanos)
                                                                                                    runtime.cc:709]   native: #09 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 0025361c  /apex/com.android.art/javalib/core-oj.jar (java.util.concurrent.LinkedTransferQueue$DualNode.await)
                                                                                                    runtime.cc:709]   native: #14 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 02488f80  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue$Transferer.xferLifo+560)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   | sysTid=12939 nice=0 cgrp=top-app sched=0/0 handle=0x78b0f4f5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 37895461 9376617 55 ) utm=3 stm=0 core=7 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78b0b4c000-0x78b0b4e000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005389c4  /apex/com.android.art/lib64/libart.so (art::Thread::Park+672) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 025b7938  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.parkNanos+200)
                                                                                                    runtime.cc:709]   native: #05 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 004c1b3c  [anon:dalvik-classes.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (???)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 004c1cd8  [anon:dalvik-classes.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (???)
                                                                                                    runtime.cc:709]   native: #15 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 004c1bbc  [anon:dalvik-classes.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (???)
                                                                                                    runtime.cc:709]   native: #20 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #29 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #30 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #31 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #32 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #33 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at jdk.internal.misc.Unsafe.park(Native method)
                                                                                                    runtime.cc:709]   - waiting on an unknown object
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
                                                                                                    runtime.cc:709]   at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.park(CoroutineScheduler.kt:795)
                                                                                                    runtime.cc:709]   at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.tryPark(CoroutineScheduler.kt:740)
                                                                                                    runtime.cc:709]   at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:711)
                                                                                                    runtime.cc:709]   at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "DefaultDispatcher-worker-3" prio=5 tid=39 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x23c25f0 self=0xb4000078dc8aa000
                                                                                                    runtime.cc:709]   | sysTid=12941 nice=0 cgrp=top-app sched=0/0 handle=0x78ae73b5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 500846 1933231 3 ) utm=0 stm=0 core=6 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78ae338000-0x78ae33a000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005389c4  /apex/com.android.art/lib64/libart.so (art::Thread::Park+672) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 025b7938  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.parkNanos+200)
                                                                                                    runtime.cc:709]   native: #05 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 004c1b3c  [anon:dalvik-classes.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (???)
                                                                                                    runtime.cc:709]   native: #10 pc 0031c3b0  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToInterpreterBridge+412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 006791f4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2068) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #04 pc 025b0420  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.park+176)
                                                                                                    runtime.cc:709]   native: #05 pc 025b02a8  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block+136)
                                                                                                    runtime.cc:709]   native: #06 pc 025aff94  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ForkJoinPool.unmanagedBlock+164)
                                                                                                    runtime.cc:709]   native: #07 pc 025afe20  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ForkJoinPool.managedBlock+224)
                                                                                                    runtime.cc:709]   native: #08 pc 025af928  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await+504)
                                                                                                    runtime.cc:709]   native: #09 pc 025af58c  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.LinkedBlockingQueue.take+204)
                                                                                                    runtime.cc:709]   native: #10 pc 0219ed40  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.getTask+560)
                                                                                                    runtime.cc:709]   native: #11 pc 0219e5b8  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+184)
                                                                                                    runtime.cc:709]   native: #12 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #13 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #14 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #19 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at jdk.internal.misc.Unsafe.park(Native method)
                                                                                                    runtime.cc:709]   - waiting on an unknown object
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3805)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3746)
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
                                                                                                    runtime.cc:709]   at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1082)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
                                                                                                    runtime.cc:709]   at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "process reaper" prio=10 tid=42 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x23d1b78 self=0xb4000078dc8bb800
                                                                                                    runtime.cc:709]   | sysTid=12945 nice=-8 cgrp=top-app sched=0/0 handle=0x78aab1d5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 1307310 2235845 9 ) utm=0 stm=0 core=7 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78aaa12000-0x78aaa14000 stackSize=1069KB
                                                                                                    runtime.cc:709]   | held mutexes=
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   | state=S schedstat=( 4803152 4212077 79 ) utm=0 stm=0 core=3 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7ab40e0000-0x7ab40e2000 stackSize=125KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 00102788  /apex/com.android.runtime/lib64/bionic/libc.so (nanosleep+8) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 000e64c0  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so (FMOD_OS_Time_Sleep+56)
                                                                                                    runtime.cc:709]   (no managed stack frames)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "AsyncTask #2" prio=5 tid=44 Waiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x23dcfa0 self=0xb4000078dc913000
                                                                                                    runtime.cc:709]   | sysTid=12947 nice=0 cgrp=top-app sched=0/0 handle=0x78a86015f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 1463384 294384 6 ) utm=0 stm=0 core=5 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x78a81fe000-0x78a8200000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf45c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005388e0  /apex/com.android.art/lib64/libart.so (art::Thread::Park+444) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 025b0420  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.park+176)
                                                                                                    runtime.cc:709]   native: #05 pc 025b6ea0  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.LinkedTransferQueue$DualNode.await+928)
                                                                                                    runtime.cc:709]   native: #06 pc 02488f80  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue$Transferer.xferLifo+560)
                                                                                                    runtime.cc:709]   native: #07 pc 02488c60  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.SynchronousQueue.xfer+144)
                                                                                                    runtime.cc:709]   native: #08 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 0067900c  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+1580) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 005c95d0  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+868) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 0025a2f0  /apex/com.android.art/javalib/core-oj.jar (java.util.concurrent.SynchronousQueue.take)
                                                                                                    runtime.cc:709]   native: #13 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 0219ed40  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.getTask+560)
                                                                                                    runtime.cc:709]   native: #17 pc 0219e5b8  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor.runWorker+184)
                                                                                                    runtime.cc:709]   native: #18 pc 0219e450  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.ThreadPoolExecutor$Worker.run+128)
                                                                                                    runtime.cc:709]   native: #19 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709] "kotlinx.coroutines.DefaultExecutor" prio=5 tid=48 TimedWaiting
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x23cff40 self=0xb4000078dc8abc00
                                                                                                    runtime.cc:709]   | sysTid=12958 nice=0 cgrp=top-app sched=0/0 handle=0x7881b055f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 2253693 509615 6 ) utm=0 stm=0 core=2 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7881702000-0x7881704000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 000bf460  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+32) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 005389c4  /apex/com.android.art/lib64/libart.so (art::Thread::Park+672) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #02 pc 00538298  /apex/com.android.art/lib64/libart.so (art::Unsafe_park +160) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #03 pc 023eb03c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 021ba960  /memfd:jit-cache (deleted) (offset 2000000) (java.util.concurrent.locks.LockSupport.parkNanos+240)
                                                                                                    runtime.cc:709]   native: #05 pc 0032d460  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #06 pc 00679208  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>+2088) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #07 pc 005c976c  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>+1280) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #08 pc 0031c9b8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 00485ad8  [anon:dalvik-classes.dex extracted in memory from /data/app/~~kGEz00H-g2HVaPM3EEtEsg==/org.levimc.launcher-iv8muP-S5qiR2GryeDXV_Q==/base.apk] (???)
                                                                                                    runtime.cc:709]   native: #10 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #11 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #12 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #13 pc 02004568  /memfd:jit-cache (deleted) (offset 2000000) (java.lang.Thread.run+136)
                                                                                                    runtime.cc:709]   native: #14 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 004bf940  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallbackWithUffdGc+8) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #19 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   at jdk.internal.misc.Unsafe.park(Native method)
                                                                                                    runtime.cc:709]   - waiting on an unknown object
                                                                                                    runtime.cc:709]   at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
                                                                                                    runtime.cc:709]   at kotlinx.coroutines.DefaultExecutor.run(DefaultExecutor.kt:121)
                                                                                                    runtime.cc:709]   at java.lang.Thread.run(Thread.java:1119)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "AudioTrack" prio=10 tid=51 Native
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #12 pc 0032f070  /data/data/org.levimc.launcher/code_cache/dex/classes2.dex (org.fmod.AudioDevice.write)
                                                                                                    runtime.cc:709]   native: #13 pc 002dc564  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute +332) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #14 pc 002dbd98  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+888) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #15 pc 00344638  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #16 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #17 pc 0032b228  /apex/com.android.art/lib64/libart.so (art::JValue art::InvokeVirtualOrInterfaceWithVarArgs<_jmethodID*>+924) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #18 pc 0066222c  /apex/com.android.art/lib64/libart.so (art::JNI<true>::CallVoidMethodV+116) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #19 pc 0035524c  /apex/com.android.art/lib64/libart.so (art::::CheckJNI::CallMethodV +412) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #20 pc 005e7b00  /apex/com.android.art/lib64/libart.so (art::::CheckJNI::CallVoidMethodV +72) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #21 pc 000e8438  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so (???)
                                                                                                    runtime.cc:709]   at android.media.AudioTrack.native_write_short(Native method)
                                                                                                    runtime.cc:709]   at android.media.AudioTrack.write(AudioTrack.java:3335)
                                                                                                    runtime.cc:709]   at android.media.AudioTrack.write(AudioTrack.java:3267)
                                                                                                    runtime.cc:709]   at org.fmod.AudioDevice.write(AudioDevice.java:80)
                                                                                                    runtime.cc:709] 
                                                                                                    runtime.cc:709] "AudioPortEventHandler" prio=5 tid=53 Native
                                                                                                    runtime.cc:709]   | group="" sCount=1 ucsCount=0 flags=1 obj=0x24f4030 self=0xb4000078d9741000
                                                                                                    runtime.cc:709]   | sysTid=12967 nice=0 cgrp=top-app sched=0/0 handle=0x7a1583d5f0
                                                                                                    runtime.cc:709]   | state=S schedstat=( 503153 4015922 6 ) utm=0 stm=0 core=6 HZ=100
                                                                                                    runtime.cc:709]   | stack=0x7a1543a000-0x7a1543c000 stackSize=4109KB
                                                                                                    runtime.cc:709]   | held mutexes=
                                                                                                    runtime.cc:709]   native: #00 pc 00102fc8  /apex/com.android.runtime/lib64/bionic/libc.so (__epoll_pwait+8) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #01 pc 00013004  /system/lib64/libutils.so (android::Looper::pollOnce+212) (BuildId: 57a4403f908f06aef0772903d0e965c3)
                                                                                                    runtime.cc:709]   native: #02 pc 00194c0c  /system/lib64/libandroid_runtime.so (android::android_os_MessageQueue_nativePollOnce+44) (BuildId: 407312aeb216060d41c1d46e91beb8de)
                                                                                                    runtime.cc:709]   native: #03 pc 023e4a4c  /memfd:jit-cache (deleted) (offset 2000000) (art_jni_trampoline+140)
                                                                                                    runtime.cc:709]   native: #04 pc 0205b7cc  /memfd:jit-cache (deleted) (offset 2000000) (android.os.MessageQueue.next+332)
                                                                                                    runtime.cc:709]   native: #05 pc 0205abd4  /memfd:jit-cache (deleted) (offset 2000000) (android.os.Looper.loopOnce+164)
                                                                                                    runtime.cc:709]   native: #06 pc 0205987c  /memfd:jit-cache (deleted) (offset 2000000) (android.os.Looper.loop+252)
                                                                                                    runtime.cc:709]   native: #07 pc 021baf68  /memfd:jit-cache (deleted) (offset 2000000) (android.os.HandlerThread.run+312)
                                                                                                    runtime.cc:709]   native: #08 pc 0032d194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #09 pc 002de270  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke+216) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
                                                                                                    runtime.cc:709]   native: #10 pc 004bfcf4  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback+932) (BuildId: 80d2ab18f9d259d8e546c1e6bae752b1)
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:709]   native: #15 pc 0e59ea74  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #16 pc 0e59e690  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #17 pc 0e5c3a4c  /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so (???) (BuildId: be68fb707697b63753a478ce392046a31731dd5c)
                                                                                                    runtime.cc:709]   native: #18 pc 000a8d88  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   native: #19 pc 0009a4a0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 4f9ec6c2efd8ecb128681ec832af69ad)
                                                                                                    runtime.cc:709]   (no managed stack frames)
                                                                                                    runtime.cc:709] Pending exception java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process org.levimc.launcher. Make sure to call FirebaseApp.initializeApp(Context) first.
                                                                                                    runtime.cc:709]   at com.google.firebase.FirebaseApp com.google.firebase.FirebaseApp.getInstance() (FirebaseApp.java:184)
                                                                                                    runtime.cc:709]   at com.google.firebase.messaging.FirebaseMessaging com.google.firebase.messaging.FirebaseMessaging.getInstance() (FirebaseMessaging.java:135)
                                                                                                    runtime.cc:709]   at void com.mojang.minecraftpe.NotificationListenerService.retrieveDeviceToken() (NotificationListenerService.java:71)
                                                                                                    runtime.cc:709]   at java.lang.String com.mojang.minecraftpe.NotificationListenerService.getDeviceRegistrationToken() (NotificationListenerService.java:60)
                                                                                                    runtime.cc:709] 
2025-07-30 05:26:51.312 12838-12929 levimc.launcher         org.levimc.launcher                  A  runtime.cc:717] JNI DETECTED ERROR IN APPLICATION: JNI GetStringUTFChars called with pending exception java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process org.levimc.launcher. Make sure to call FirebaseApp.initializeApp(Context) first.
                                                                                                    runtime.cc:717]   at com.google.firebase.FirebaseApp com.google.firebase.FirebaseApp.getInstance() (FirebaseApp.java:184)
                                                                                                    runtime.cc:717]   at com.google.firebase.messaging.FirebaseMessaging com.google.firebase.messaging.FirebaseMessaging.getInstance() (FirebaseMessaging.java:135)
                                                                                                    runtime.cc:717]   at void com.mojang.minecraftpe.NotificationListenerService.retrieveDeviceToken() (NotificationListenerService.java:71)
                                                                                                    runtime.cc:717]   at java.lang.String com.mojang.minecraftpe.NotificationListenerService.getDeviceRegistrationToken() (NotificationListenerService.java:60)
                                                                                                    runtime.cc:717] 
                                                                                                    runtime.cc:717]     in call to GetStringUTFChars
2025-07-30 05:26:51.312 12838-12929 Bedrock                 org.levimc.launcher                  W  Breakpad _filterCallback called
2025-07-30 05:26:51.720 12838-12949 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp
2025-07-30 05:26:51.722 12838-12949 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"307ff7a867a04f2487c5f1e9fa054bb1","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","flavor":"Publish","osVersion":"Android 15","sessionID":"a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee","versionCode":"972109401"}}
2025-07-30 05:26:51.723 12838-12949 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp
2025-07-30 05:26:51.723 12838-12949 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-30 05:26:51.723 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-30 05:26:51.723 12838-12949 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-30 05:26:51.723 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-30 05:26:51.723 12838-12949 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-30 05:26:51.724 12838-12949 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/a6fdf88f-653e-4c8a-b3cb-c5bc2aa498ee.dmp 1 more time(s) after 1000 ms
2025-07-30 05:26:51.744 12838-12929 Bedrock                 org.levimc.launcher                  W  Breakpad _dumpCallback called, dump path: /data/user/0/org.levimc.launcher/crash/7f1bd4c5-9141-41c1-b8fc-b15bdd6a06ed.dmp
---------------------------- PROCESS ENDED (12838) for package org.levimc.launcher ----------------------------
