2025-07-30 05:45:18.328 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-30 05:45:18.334 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@e247023
2025-07-30 05:45:18.426 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-30 05:45:18.444 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-30 05:45:18.449 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes2.dex
2025-07-30 05:45:18.452 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes.dex
2025-07-30 05:45:18.452 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: launcher.dex
2025-07-30 05:45:18.452 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.452 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.463 17495-17495 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-30 05:45:18.504 17495-17495 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@d113a6e
2025-07-30 05:45:18.505 17495-17495 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@2d13fa5
2025-07-30 05:45:18.505 17495-17495 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{31a8f7a V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:331 
2025-07-30 05:45:18.508 17495-17495 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 05:45:18.513 17495-17524 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 05:45:18.521 17495-17495 InputTransport          org.levimc.launcher                  D  Input channel constructed: '95d0bff', fd=150
2025-07-30 05:45:18.522 17495-17495 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:45:18.522 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 05:45:18.522 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@31a8f7a IsHRR=false TM=true
2025-07-30 05:45:18.532 17495-17495 BufferQueueConsumer     org.levimc.launcher                  D  [](id:************,api:0,p:-1,c:17495) connect: controlledByApp=false
2025-07-30 05:45:18.532 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@c62732b#2](f:0,a:0,s:0) constructor()
2025-07-30 05:45:18.533 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@c62732b mNativeObject= 0xb4000079e98fd000 sc.mNativeObject= 0xb4000078ed8bd340 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 05:45:18.533 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@c62732b mNativeObject= 0xb4000079e98fd000 sc.mNativeObject= 0xb4000078ed8bd340 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 05:45:18.533 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@c62732b#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-30 05:45:18.533 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=5 res=0x3 s={true 0xb4000078dbd22800} ch=true seqId=0
2025-07-30 05:45:18.534 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 05:45:18.534 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078dbd22800} hwInitialized=true
2025-07-30 05:45:18.535 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 05:45:18.535 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@c62732b#6
2025-07-30 05:45:18.535 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@c62732b#7
2025-07-30 05:45:18.535 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:45:18.537 17495-17547 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 05:45:18.538 17495-17547 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  mWNT: t=0xb4000079e98f3900 mBlastBufferQueue=0xb4000079e98fd000 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 05:45:18.539 17495-17547 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:45:18.542 17495-17524 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@c62732b#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:45:18.542 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-30 05:45:18.542 17495-17524 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@c62732b#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=293515510711648(auto) mPendingTransactions.size=0 graphicBufferId=75140452843546 transform=7
2025-07-30 05:45:18.542 17495-17524 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 05:45:18.543 17495-17524 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 17495    Tid : 17524
2025-07-30 05:45:18.544 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:45:18.550 17495-17524 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:45:18.560 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:18.560 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:18.596 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078dbd22800}
2025-07-30 05:45:18.596 17495-17495 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 05:45:18.596 17495-17495 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 05:45:18.597 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-30 05:45:18.597 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: classes2.dex
2025-07-30 05:45:18.600 17495-17545 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=157
2025-07-30 05:45:18.608 17495-17495 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-30 05:45:18.656 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-30 05:45:18.702 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-30 05:45:18.702 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: classes.dex
2025-07-30 05:45:18.703 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: launcher.dex
2025-07-30 05:45:18.707 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: launcher.dex
2025-07-30 05:45:18.707 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully added DEX to path: launcher.dex
2025-07-30 05:45:18.709 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded non-stub Minecraft classes: Launcher and MainActivity
2025-07-30 05:45:18.709 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-30 05:45:18.709 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.709 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.709 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.709 17495-17596 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.710 17495-17596 LeviLogger              org.levimc.launcher                  I  [LeviMC] Firebase already initialized
2025-07-30 05:45:18.711 17495-17599 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded launcher class: com.mojang.minecraftpe.Launcher
2025-07-30 05:45:18.711 17495-17599 LeviLogger              org.levimc.launcher                  I  [LeviMC] Launcher class has onCreate method - not a stub
2025-07-30 05:45:18.716 17495-17599 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~ZcC699DIBmOu9yWe4OyZ7A==/org.levimc.launcher-1wb25zNMhMvERTq-PhDgyw==/base.apk!classes7.dex): ok
2025-07-30 05:45:18.719 17495-17599 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~ZcC699DIBmOu9yWe4OyZ7A==/org.levimc.launcher-1wb25zNMhMvERTq-PhDgyw==/base.apk!classes7.dex): ok
2025-07-30 05:45:18.847 17495-17599 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~ZcC699DIBmOu9yWe4OyZ7A==/org.levimc.launcher-1wb25zNMhMvERTq-PhDgyw==/base.apk!classes7.dex): ok
2025-07-30 05:45:18.847 17495-17599 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7a17d235f0
2025-07-30 05:45:18.847 17495-17599 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-30 05:45:18.876 17495-17495 LeviLogger              org.levimc.launcher                  I  [LeviMC] Minecraft activity started successfully
2025-07-30 05:45:18.894 17495-17495 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 05:45:18.907 17495-17495 nativeloader            org.levimc.launcher                  D  Load libmaesdk.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libmaesdk.so" not found
2025-07-30 05:45:18.907 17495-17495 MCPE                    org.levimc.launcher                  D  maesdk library not found. This is expected if we're not in Edu mode
2025-07-30 05:45:18.908 17495-17495 nativeloader            org.levimc.launcher                  D  Load libovrfmod.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrfmod.so" not found
2025-07-30 05:45:18.908 17495-17495 MCPE                    org.levimc.launcher                  D  OVRfmod library not found
2025-07-30 05:45:18.909 17495-17495 nativeloader            org.levimc.launcher                  D  Load libovrplatformloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrplatformloader.so" not found
2025-07-30 05:45:18.909 17495-17495 MCPE                    org.levimc.launcher                  D  OVRplatform library not found
2025-07-30 05:45:18.918 17495-17495 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-30 05:45:18.918 17495-17495 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~ZcC699DIBmOu9yWe4OyZ7A==/org.levimc.launcher-1wb25zNMhMvERTq-PhDgyw==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-30 05:45:18.918 17495-17495 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-30 05:45:18.918 17495-17495 nativeloader            org.levimc.launcher                  D  Load /data/app/~~ZcC699DIBmOu9yWe4OyZ7A==/org.levimc.launcher-1wb25zNMhMvERTq-PhDgyw==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/launcher.dex): ok
2025-07-30 05:45:18.930 17495-17495 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-30 05:45:18.931 17495-17540 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:45:18.949 17495-17495 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::onCreate
2025-07-30 05:45:18.953 17495-17495 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@9542156
2025-07-30 05:45:18.956 17495-17495 gti.InputConnection     org.levimc.launcher                  D  InputConnection created
2025-07-30 05:45:18.959 17495-17495 GameActivity            org.levimc.launcher                  I  Looking for library libpreloader.so
2025-07-30 05:45:18.959 17495-17495 GameActivity            org.levimc.launcher                  I  Found library libpreloader.so. Loading...
2025-07-30 05:45:18.965 17495-17495 GameActivity            org.levimc.launcher                  D  GameActivity_register
2025-07-30 05:45:18.965 17495-17495 GameActivity            org.levimc.launcher                  D  SDK version: 35
2025-07-30 05:45:18.967 17495-17602 Minecraft               org.levimc.launcher                  I  android_main starting. internalDataPath is '/data/user/0/org.levimc.launcher/files', externalDataPath is '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-30 05:45:18.971 17495-17602 MCPE                    org.levimc.launcher                  E  *** setCachedDeviceId(307ff7a867a04f2487c5f1e9fa054bb1)
2025-07-30 05:45:18.972 17495-17602 Bedrock                 org.levimc.launcher                  I  Breakpad config: directory is: /data/user/0/org.levimc.launcher/crash, sessionid is: 7656ac34-2821-4208-a9c2-59418feb1da2
2025-07-30 05:45:18.977 17495-17602 libc                    org.levimc.launcher                  W  Access denied finding property "ro.mediatek.platform"
2025-07-30 05:45:18.983 17495-17602 System.out              org.levimc.launcher                  I  getwidth: 2340
2025-07-30 05:45:18.983 17495-17602 System.out              org.levimc.launcher                  I  getheight: 1080
2025-07-30 05:45:18.995 17495-17602 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-30 05:45:19.050 17495-17495 AppExitInfoHelper       org.levimc.launcher                  I  Registering session ID for ApplicationExitInfo: 7656ac34-2821-4208-a9c2-59418feb1da2
2025-07-30 05:45:19.062 17495-17495 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze.configure() called with configuration: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:45:19.062 17495-17495 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::requestPushPermission
2025-07-30 05:45:19.066 17495-17602 SwappyDisplayManager    org.levimc.launcher                  I  Using internal com/google/androidgamesdk/SwappyDisplayManager class from dex bytes.
2025-07-30 05:45:19.066 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:31)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:45:19.069 17495-17602 SwappyDisplayManager    org.levimc.launcher                  E  dalvik.system.InMemoryDexClassLoader[DexPathList[[dex file "InMemoryDexFile[cookie=[0, -5476376627545917408]]"],nativeLibraryDirectories=[/system/lib64, /system_ext/lib64]]] couldn't find "libpreloader.so"
2025-07-30 05:45:19.069 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:32)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:45:19.072 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:33)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:45:19.075 17495-17607 SwappyDisplayManager    org.levimc.launcher                  I  Starting looper thread
2025-07-30 05:45:19.076 17495-17602 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000078eae54640
2025-07-30 05:45:19.076 17495-17602 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-30 05:45:19.076 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:34)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:45:19.080 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:37)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:31)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-30 05:45:19.100 17495-17495 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-30 05:45:19.101 17495-17495 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 303326708; UID 10645; state: ENABLED
2025-07-30 05:45:19.103 17495-17495 MinecraftPE             org.levimc.launcher                  D  onStart
2025-07-30 05:45:19.111 17495-17602 AppExitInfoHelper       org.levimc.launcher                  I  Received session ID from ApplicationExitInfo: 0785b564-f816-4516-abe6-ee4d0cdeb2fa
2025-07-30 05:45:19.121 17495-17602 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-30 05:45:19.128 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id -1- is Crete Controller: false
2025-07-30 05:45:19.128 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id 2- is Crete Controller: false
2025-07-30 05:45:19.128 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id 3- is Crete Controller: false
2025-07-30 05:45:19.128 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id 4- is Crete Controller: false
2025-07-30 05:45:19.128 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id 5- is Crete Controller: false
2025-07-30 05:45:19.129 17495-17495 Minecraft               org.levimc.launcher                  W  INPUT device id 6- is Crete Controller: false
2025-07-30 05:45:19.129 17495-17495 Minecraft               org.levimc.launcher                  W  No Xbox Controller Found
2025-07-30 05:45:19.129 17495-17495 Minecraft               org.levimc.launcher                  W  No Playstation Controller Found
2025-07-30 05:45:19.129 17495-17495 Minecraft               org.levimc.launcher                  W  No PS Dualsense Controller Found
2025-07-30 05:45:19.131 17495-17495 MinecraftPE             org.levimc.launcher                  D  onResume
2025-07-30 05:45:19.142 17495-17495 Braze v24....ageManager org.levimc.launcher                  V  Registering InAppMessageManager with activity: com.mojang.minecraftpe.Launcher
2025-07-30 05:45:19.145 17495-17495 Braze v24....ageManager org.levimc.launcher                  D  Subscribing in-app message event subscriber
2025-07-30 05:45:19.146 17495-17495 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-30 05:45:19.146 17495-17495 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-30 05:45:19.146 17495-17495 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK Initializing
2025-07-30 05:45:19.149 17495-17540 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:45:19.163 17495-17620 Braze v24....mageLoader org.levimc.launcher                  D  Initializing disk cache
2025-07-30 05:45:19.164 17495-17495 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK loaded in 18 ms.
2025-07-30 05:45:19.164 17495-17495 Braze v24....ageManager org.levimc.launcher                  V  Subscribing sdk data wipe subscriber
2025-07-30 05:45:19.165 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Applying any pending runtime configuration values
2025-07-30 05:45:19.165 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Setting pending config object: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:45:19.166 17495-17623 Braze v24....onProvider org.levimc.launcher                  I  Setting Braze Override configuration with config: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-30 05:45:19.166 17495-17623 Braze v24....onProvider org.levimc.launcher                  I  Found an override api key. Using it to configure the Braze SDK
2025-07-30 05:45:19.167 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml INTEGER configuration value with primary key 'com_braze_logger_initial_log_level'. Using default value '4'.
2025-07-30 05:45:19.167 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_logger_initial_log_level' and value: '4'
2025-07-30 05:45:19.169 17495-17620 Braze v24....mageLoader org.levimc.launcher                  D  Disk cache initialized
2025-07-30 05:45:19.177 17495-17495 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 05:45:19.178 17495-17524 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 05:45:19.179 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_custom_endpoint'. Using default value 'null'.
2025-07-30 05:45:19.179 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_endpoint' and value: 'null'
2025-07-30 05:45:19.179 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_firebase_cloud_messaging_registration_enabled' and value: 'false'
2025-07-30 05:45:19.179 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic Firebase Cloud Messaging registration not enabled in configuration. Braze will not register for Firebase Cloud Messaging.
2025-07-30 05:45:19.179 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_push_adm_messaging_registration_enabled' and value: 'false'
2025-07-30 05:45:19.179 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic ADM registration not enabled in configuration. Braze will not register for ADM.
2025-07-30 05:45:19.180 17495-17495 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-30 05:45:19.180 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Starting up a new user dependency manager
2025-07-30 05:45:19.187 17495-17495 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'cce568c', fd=169
2025-07-30 05:45:19.187 17495-17495 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:45:19.188 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 05:45:19.188 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@9bbbc9b IsHRR=false TM=true
2025-07-30 05:45:19.190 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  onWindowVisibilityChanged(0) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ......I. 0,0-0,0} of VRI[Launcher]@b79d09e
2025-07-30 05:45:19.190 17495-17495 SurfaceView             org.levimc.launcher                  D  251191725 updateSurface: has no frame
2025-07-30 05:45:19.192 17495-17495 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:45:19.192 17495-17495 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:45:19.199 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_session_timeout' and value: '10'
2025-07-30 05:45:19.200 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_session_start_based_timeout_enabled'. Using default value 'false'.
2025-07-30 05:45:19.200 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_session_start_based_timeout_enabled' and value: 'false'
2025-07-30 05:45:19.214 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_trigger_action_minimum_time_interval_seconds' and value: '5'
2025-07-30 05:45:19.217 17495-17495 BufferQueueConsumer     org.levimc.launcher                  D  [](id:445700000003,api:0,p:-1,c:17495) connect: controlledByApp=false
2025-07-30 05:45:19.217 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@b79d09e#3](f:0,a:0,s:0) constructor()
2025-07-30 05:45:19.217 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@b79d09e mNativeObject= 0xb4000078dc2e9800 sc.mNativeObject= 0xb4000078dc21d280 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 05:45:19.217 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@b79d09e mNativeObject= 0xb4000078dc2e9800 sc.mNativeObject= 0xb4000078dc21d280 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 05:45:19.217 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@b79d09e#3](f:0,a:0,s:0) update width=2340 height=1080 format=-3 mTransformHint=4
2025-07-30 05:45:19.217 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=23 res=0x3 s={true 0xb4000078dc366800} ch=true seqId=0
2025-07-30 05:45:19.218 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 05:45:19.219 17495-17495 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:45:19.220 17495-17495 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:45:19.220 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078dc366800} hwInitialized=true
2025-07-30 05:45:19.220 17495-17495 SurfaceView             org.levimc.launcher                  D  251191725 updateSurface: has no frame
2025-07-30 05:45:19.221 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  windowStopped(false) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ......ID 0,0-2340,1080} of VRI[Launcher]@b79d09e
2025-07-30 05:45:19.221 17495-17495 SurfaceView             org.levimc.launcher                  D  251191725 updateSurface: has no frame
2025-07-30 05:45:19.221 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 05:45:19.221 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 Changes: creating=true format=true size=true visible=true alpha=false hint=true visible=true left=true top=true z=false attached=true lifecycleStrategy=false
2025-07-30 05:45:19.227 17495-17495 BufferQueueConsumer     org.levimc.launcher                  D  [](id:445700000004,api:0,p:-1,c:17495) connect: controlledByApp=false
2025-07-30 05:45:19.227 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) constructor()
2025-07-30 05:45:19.227 17495-17623 Braze v24.....bo.app.d6 org.levimc.launcher                  V  Subscribing to trigger dispatch events.
2025-07-30 05:45:19.228 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = null mNativeObject= 0xb4000078dc2e9400 sc.mNativeObject= 0xb4000078dc21db80 format= 4 caller= android.view.SurfaceView.createBlastSurfaceControls:1642 android.view.SurfaceView.updateSurface:1318 android.view.SurfaceView.lambda$new$0:268 android.view.SurfaceView.$r8$lambda$NfZyM_TG8F8lqzaOVZ7noREFjzU:0 android.view.SurfaceView$$ExternalSyntheticLambda1.onPreDraw:0 android.view.ViewTreeObserver.dispatchOnPreDraw:1226 
2025-07-30 05:45:19.228 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-30 05:45:19.228 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 Cur surface: Surface(name=null mNativeObject=0)/@0x9769f68
2025-07-30 05:45:19.228 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  pST: sr = Rect(0, 0 - 2340, 1080) sw = 2340 sh = 1080
2025-07-30 05:45:19.229 17495-17495 SurfaceView             org.levimc.launcher                  D  251191725 performSurfaceTransaction RenderWorker position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-30 05:45:19.229 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  updateSurface: mVisible = true mSurface.isValid() = true
2025-07-30 05:45:19.229 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  updateSurface: mSurfaceCreated = false surfaceChanged = true visibleChanged = true
2025-07-30 05:45:19.229 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 visibleChanged -- surfaceCreated
2025-07-30 05:45:19.229 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  surfaceCreated 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ......ID 0,0-2340,1080}
2025-07-30 05:45:19.232 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_custom_location_providers_list'. Using default value '[]'.
2025-07-30 05:45:19.232 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_location_providers_list' and value: '[]'
2025-07-30 05:45:19.233 17495-17623 Braze v24....nceManager org.levimc.launcher                  D  Did not find stored geofences.
2025-07-30 05:45:19.236 17495-17623 Braze v24....nceManager org.levimc.launcher                  I  Geofences implicitly disabled via server configuration.
2025-07-30 05:45:19.236 17495-17623 Braze v24....nceManager org.levimc.launcher                  I  ***Geofence API not found. Please include the android-sdk-location module***
2025-07-30 05:45:19.236 17495-17623 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Geofences not set up.
2025-07-30 05:45:19.237 17495-17623 Braze v24.3.0 .o        org.levimc.launcher                  I  ***Location API not found. Please include android-sdk-location module***
2025-07-30 05:45:19.239 17495-17623 Braze v24.3.0 .f1       org.levimc.launcher                  D  Did not find stored feature flags.
2025-07-30 05:45:19.255 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: NONE
2025-07-30 05:45:19.255 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:45:19.255 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  D  Data sync started
2025-07-30 05:45:19.256 17495-17623 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5461)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5428)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5402)] [bo.app.i0.d(SourceFile:3)] [bo.app.i0.e(SourceFile:6)] [bo.app.i0.a(SourceFile:10)] [bo.app.n6.<init>(SourceFile:227)] [com.braze.Braze$d.a(SourceFile:63)] [com.braze.Braze$d.invoke(SourceFile:1)] [com.braze.Braze$r2$a.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:280)] [kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:85)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:59)] [kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source:1)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:38)] [kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source:1)] [com.braze.Braze$r2.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)] [java.lang.Thread.run(Thread.java:1119)]
2025-07-30 05:45:19.260 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:45:19.260 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:45:19.260 17495-17606 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:45:19.260 17495-17606 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:45:19.265 17495-17623 Braze v24.3.0 .c1       org.levimc.launcher                  D  Started offline event recovery task.
2025-07-30 05:45:19.269 17495-17623 Braze v24.3.0 .c1       org.levimc.launcher                  V  Adding event to dispatch from storage: {"name":"ss","data":{},"time":1.75383270058E9,"session_id":"5b9f6002-3889-49ab-94bf-38c7b176eef1"}
2025-07-30 05:45:19.269 17495-17623 Braze v24.3.0 .c1       org.levimc.launcher                  V  Adding event to dispatch from storage: {"name":"se","data":{"d":85},"time":1.75383270057E9,"session_id":"0c52fd69-1cec-4bfd-b38f-52c74a648c95"}
2025-07-30 05:45:19.271 17495-17602 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-30 05:45:19.271 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 surfaceChanged -- format=4 w=2340 h=1080
2025-07-30 05:45:19.271 17495-17602 Minecraft               org.levimc.launcher                  W  MinecraftGame::init && MinecraftGame::setSize!
2025-07-30 05:45:19.271 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  surfaceChanged (2340,1080) 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ......ID 0,0-2340,1080}
2025-07-30 05:45:19.272 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 surfaceRedrawNeeded
2025-07-30 05:45:19.273 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 finishedDrawing
2025-07-30 05:45:19.273 17495-17495 SurfaceView             org.levimc.launcher                  V  Layout: x=0 y=0 w=2340 h=1080, frame=Rect(0, 0 - 2340, 1080)
2025-07-30 05:45:19.275 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@b79d09e#9
2025-07-30 05:45:19.275 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@b79d09e#10
2025-07-30 05:45:19.277 17495-17615 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000078acec1400
2025-07-30 05:45:19.277 17495-17615 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-30 05:45:19.278 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:45:19.279 17495-17524 SurfaceView             org.levimc.launcher                  D  251191725 updateSurfacePosition RenderWorker, frameNr = 1, position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-30 05:45:19.279 17495-17524 SurfaceView@ef8e1ad     org.levimc.launcher                  I  uSP: rtp = Rect(0, 0 - 2340, 1080) rtsw = 2340 rtsh = 1080
2025-07-30 05:45:19.279 17495-17524 SurfaceView@ef8e1ad     org.levimc.launcher                  I  onSSPAndSRT: pl = 0 pt = 0 sx = 1.0 sy = 1.0
2025-07-30 05:45:19.279 17495-17524 SurfaceView@ef8e1ad     org.levimc.launcher                  I  aOrMT: VRI[Launcher]@b79d09e t = android.view.SurfaceControl$Transaction@4befa47 fN = 1 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 android.graphics.RenderNode$CompositePositionUpdateListener.positionChanged:398 
2025-07-30 05:45:19.280 17495-17524 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  mWNT: t=0xb4000078dbf29700 mBlastBufferQueue=0xb4000078dc2e9800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SurfaceView.applyOrMergeTransaction:1723 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 
2025-07-30 05:45:19.280 17495-17548 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 05:45:19.280 17495-17548 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  mWNT: t=0xb4000078dc4aa000 mBlastBufferQueue=0xb4000078dc2e9800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 05:45:19.281 17495-17548 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:45:19.291 17495-17524 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@b79d09e#3](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:45:19.292 17495-17524 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@b79d09e#3](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=293516259716187(auto) mPendingTransactions.size=0 graphicBufferId=75140452843556 transform=7
2025-07-30 05:45:19.292 17495-17524 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 05:45:19.293 17495-17524 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 17495    Tid : 17524
2025-07-30 05:45:19.294 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:45:19.300 17495-17524 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:45:19.301 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:19.301 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:19.304 17495-17495 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.performTraversals:4497 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-30 05:45:19.304 17495-17495 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@b79d09e mNativeObject= 0xb4000078dc2e9800 sc.mNativeObject= 0xb4000078dc21d280 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-30 05:45:19.305 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078dc366800} ch=false seqId=0
2025-07-30 05:45:19.306 17495-17495 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:45:19.307 17495-17495 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:45:19.307 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@6824012 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x4e6f3e3 frame=2
2025-07-30 05:45:19.308 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-30 05:45:19.311 17495-17547 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  mWNT: t=0xb4000078ed8c7f00 mBlastBufferQueue=0xb4000078dc2e9800 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-30 05:45:19.324 17495-17495 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:45:19.325 17495-17495 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:45:19.326 17495-17495 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:45:19.328 17495-17495 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:45:19.328 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-30 05:45:19.328 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-30 05:45:19.329 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-30 05:45:19.333 17495-17495 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:45:19.333 17495-17495 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:45:19.334 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@b79d09e#11
2025-07-30 05:45:19.334 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@b79d09e#12
2025-07-30 05:45:19.334 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:45:19.335 17495-17548 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-30 05:45:19.335 17495-17548 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:45:19.339 17495-17524 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-30 05:45:19.340 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:45:19.344 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-30 05:45:19.345 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078dc366800}
2025-07-30 05:45:19.346 17495-17495 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 05:45:19.346 17495-17495 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 05:45:19.348 17495-17495 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.xbox.httpclient.NetworkObserver.Initialize(NetworkObserver.java:72)] [com.mojang.minecraftpe.MainActivity.nativeRunNativeCallbackOnUiThread(Native Method)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2136)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2133)] [java.util.concurrent.FutureTask.run(FutureTask.java:317)]
2025-07-30 05:45:19.349 17495-17512 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=187
2025-07-30 05:45:19.379 17495-17495 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-30 05:45:19.384 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-30 05:45:19.384 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-30 05:45:19.394 17495-17613 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 312399441; UID 10645; state: ENABLED
2025-07-30 05:45:19.410 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@c62732b#2](f:0,a:3,s:0) destructor()
2025-07-30 05:45:19.410 17495-17495 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@c62732b#2(BLAST Consumer)2](id:************,api:0,p:-1,c:17495) disconnect
2025-07-30 05:45:19.411 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=19 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-30 05:45:19.411 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-30 05:45:19.414 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-30 05:45:19.414 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-30 05:45:19.417 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  stopped(true) old = false
2025-07-30 05:45:19.417 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-30 05:45:19.420 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  stopped(true) old = false
2025-07-30 05:45:19.420 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-30 05:45:19.424 17495-17503 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=157
2025-07-30 05:45:19.425 17495-17503 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=187
2025-07-30 05:45:19.425 17495-17495 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@4a62976
2025-07-30 05:45:19.426 17495-17504 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 
2025-07-30 05:45:19.430 17495-17504 System                  org.levimc.launcher                  W  A resource failed to call close. 
2025-07-30 05:45:19.433 17495-17623 Braze v24.3.0 .q        org.levimc.launcher                  D  Messaging session stopped. Adding new messaging session timestamp: 1753832719
2025-07-30 05:45:19.434 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Closed session with activity: ui.activities.MainActivity
2025-07-30 05:45:19.434 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Getting the stored open session
2025-07-30 05:45:19.436 17495-17524 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@e247023#1](f:0,a:3,s:0) destructor()
2025-07-30 05:45:19.436 17495-17524 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@e247023#1(BLAST Consumer)1](id:445700000001,api:0,p:-1,c:17495) disconnect
2025-07-30 05:45:19.437 17495-17495 VRI[MainAc...y]@e247023 org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-30 05:45:19.437 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 5b9f6002-3889-49ab-94bf-38c7b176eef1
2025-07-30 05:45:19.437 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  I  Session [5b9f6002-3889-49ab-94bf-38c7b176eef1] being sealed because its end time is over the grace period. Session: 
                                                                                                    MutableSession(sessionId=5b9f6002-3889-49ab-94bf-38c7b176eef1, startTime=1.753832700578E9, endTime=1.753832700594E9, isSealed=false, duration=0)
2025-07-30 05:45:19.438 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.f5 fired: SessionSealedEvent(sealedSession=
                                                                                                    MutableSession(sessionId=5b9f6002-3889-49ab-94bf-38c7b176eef1, startTime=1.753832700578E9, endTime=1.753832719437E9, isSealed=true, duration=18))
2025-07-30 05:45:19.439 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.f5 on 1 subscribers.
2025-07-30 05:45:19.440 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding session id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 18
                                                                                                      },
                                                                                                      "time": 1.753832719439E9,
                                                                                                      "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                    }
2025-07-30 05:45:19.440 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 18
                                                                                                      },
                                                                                                      "time": 1.753832719439E9,
                                                                                                      "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                    }
2025-07-30 05:45:19.441 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 18
                                                                                                      },
                                                                                                      "time": 1.753832719439E9,
                                                                                                      "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                    }
2025-07-30 05:45:19.442 17495-17495 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '551a527', fd=218
2025-07-30 05:45:19.445 17495-17620 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid ca795f06-0758-438e-a049-65ac8cc44ab3
2025-07-30 05:45:19.447 17495-17495 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{31a8f7a V.ED..... R.....ID 0,0-376,410 aid=1073741825}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:331)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-30 05:45:19.447 17495-17495 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{31a8f7a V.ED..... R.....ID 0,0-376,410 aid=1073741825}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-30 05:45:19.449 17495-17495 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@1322946
2025-07-30 05:45:19.451 17495-17495 VRI[MainAc...y]@c62732b org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-30 05:45:19.451 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"se","data":{"d":18},"time":1.753832719439E9,"session_id":"5b9f6002-3889-49ab-94bf-38c7b176eef1"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:45:19.451 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='5b9f6002-3889-49ab-94bf-38c7b176eef1', eventType='SESSION_ENDED'}'
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  I  New session created with ID: 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.d5 fired: SessionCreatedEvent(session=
                                                                                                    MutableSession(sessionId=5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9, startTime=1.753832719453E9, endTime=null, isSealed=false, duration=-1))
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.d5 on 2 subscribers.
2025-07-30 05:45:19.453 17495-17495 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '95d0bff', fd=150
2025-07-30 05:45:19.453 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Session start event for new session received.
2025-07-30 05:45:19.454 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
2025-07-30 05:45:19.454 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
2025-07-30 05:45:19.454 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753832719453E9,
                                                                                                      "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                    }
2025-07-30 05:45:19.454 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753832719453E9,
                                                                                                      "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                    }
2025-07-30 05:45:19.456 17495-17622 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 5a6be461-1c5a-4a72-b831-b5a9dce9b019
2025-07-30 05:45:19.456 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"ss","data":{},"time":1.753832719453E9,"session_id":"5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:45:19.456 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:19.457  1598-1804  WindowManager           system_server                        E  win=Window{95d0bff u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17 
2025-07-30 05:45:19.457 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = FLUSH_PENDING_BRAZE_EVENTS
                                                                                                    brazeEvent = null
                                                                                                    sessionId = 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
                                                                                                    brazeRequest = null
2025-07-30 05:45:19.457 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:19.458 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: true
2025-07-30 05:45:19.458 17495-17623 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:45:19.458 17495-17623 Braze v24.3.0 .l0       org.levimc.launcher                  V  Device object cache cleared.
2025-07-30 05:45:19.458 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting trigger refresh.
2025-07-30 05:45:19.460 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_server_target'. Using default value 'PROD'.
2025-07-30 05:45:19.460 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_server_target' and value: 'PROD'
2025-07-30 05:45:19.472 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:19.472 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:19.473 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.y5 fired: TriggerDispatchStartedEvent(request={
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:19.473 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.y5 on 1 subscribers.
2025-07-30 05:45:19.474 17495-17623 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:19.474 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:45:19.475 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_sdk_authentication_enabled'. Using default value 'false'.
2025-07-30 05:45:19.476 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_automatic_geofence_requests_enabled'. Using default value 'true'.
2025-07-30 05:45:19.476 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_authentication_enabled' and value: 'false'
2025-07-30 05:45:19.476 17495-17623 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_automatic_geofence_requests_enabled' and value: 'true'
2025-07-30 05:45:19.476 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting Braze Geofence refresh on session created event due to configuration.
2025-07-30 05:45:19.476 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:19.477 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_sdk_flavor'. Using default value 'null'.
2025-07-30 05:45:19.478 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_flavor' and value: 'null'
2025-07-30 05:45:19.478 17495-17602 BitmapFactory           org.levimc.launcher                  E  Unable to decode file: java.io.FileNotFoundException: /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png: open failed: ENOENT (No such file or directory)
2025-07-30 05:45:19.478 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Content Card refresh on session created event due to server configuration.
2025-07-30 05:45:19.478 17495-17602 System.err              org.levimc.launcher                  W  getImageData: Could not open image /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png
2025-07-30 05:45:19.478 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Feature Flags refresh on session created event due to server configuration.
2025-07-30 05:45:19.478 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9', eventType='SESSION_STARTED'}'
2025-07-30 05:45:19.478 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:45:19.479 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Creating a session seal alarm with a delay of 10000 ms
2025-07-30 05:45:19.484 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.i5 fired: bo.app.i5@adb222f
2025-07-30 05:45:19.484 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.i5 on 1 subscribers.
2025-07-30 05:45:19.484 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:45:19.484 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:45:19.485 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Closed session with id 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
2025-07-30 05:45:19.485 17495-17622 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@312103c
2025-07-30 05:45:19.485 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-30 05:45:19.485 17495-17623 Braze v24.3.0 .a5       org.levimc.launcher                  V  Not allowing server config info unlock. Returning null.
2025-07-30 05:45:19.487 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-30 05:45:19.487 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:19.487 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:19.487 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-30 05:45:19.487 17495-17623 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {}
                                                                                                    }
2025-07-30 05:45:19.488 17495-17622 Braze v24.3.0 .l0       org.levimc.launcher                  V  Sending full device due to NOTIFICATIONS_ENABLED true
2025-07-30 05:45:19.488 17495-17623 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Not requesting geofences.
2025-07-30 05:45:19.488 17495-17622 Braze v24.3.0 .l0       org.levimc.launcher                  V  Remote Notification setting changed to true. Updating user subscription.
2025-07-30 05:45:19.491 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-30 05:45:19.491 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-30 05:45:19.491 17495-17622 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:45:19.491 17495-17622 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@a0b61c5
2025-07-30 05:45:19.491 17495-17622 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:45:19.492 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753832719453E9,"session_id":"5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"} with uid: 5a6be461-1c5a-4a72-b831-b5a9dce9b019
2025-07-30 05:45:19.492 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.75383270058E9,"session_id":"5b9f6002-3889-49ab-94bf-38c7b176eef1"} with uid: 2030edc4-c57a-478d-8469-2972e06facca
2025-07-30 05:45:19.492 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":85},"time":1.75383270057E9,"session_id":"0c52fd69-1cec-4bfd-b38f-52c74a648c95"} with uid: ddd678e6-5f3e-4ed2-91e2-16aa05226822
2025-07-30 05:45:19.492 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":18},"time":1.753832719439E9,"session_id":"5b9f6002-3889-49ab-94bf-38c7b176eef1"} with uid: ca795f06-0758-438e-a049-65ac8cc44ab3
2025-07-30 05:45:19.492 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_internal_sdk_metadata'. Using default value '[]'.
2025-07-30 05:45:19.492 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_internal_sdk_metadata' and value: '[]'
2025-07-30 05:45:19.493 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_sdk_metadata'. Using default value '[]'.
2025-07-30 05:45:19.493 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:45:19.493 17495-17622 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:45:19.496 17495-17622 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:19.498 17495-17622 Braze v24.3.0 .l0       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:45:19.498 17495-17622 Braze v24.3.0 .m6       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:45:19.498 17495-17622 Braze v24.3.0 .t0       org.levimc.launcher                  D  Short circuiting execution of network request and immediately marking it as succeeded.
2025-07-30 05:45:19.498 17495-17622 Braze v24.3.0 .j0       org.levimc.launcher                  D  DataSyncRequest executed successfully.
2025-07-30 05:45:19.498 17495-17622 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:19.499 17495-17622 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:19.505 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "d7b43baccf4e20a"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "X-Braze-TriggersRequest" => "true"
                                                                                                    "X-Braze-DataRequest" => "true"
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:19.558 17495-17615 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:45:19.559 17495-17615 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=293516526825802(auto) mPendingTransactions.size=0 graphicBufferId=75140452843559 transform=7
2025-07-30 05:45:19.662 17495-17621 Braze v24.3.0 .q5       org.levimc.launcher                  V  Enabling SSL protocols: [TLSv1.2, TLSv1.3]
2025-07-30 05:45:20.123 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = d7b43baccf4e20a time = 618ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:45:20.125 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:45:20.125 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:45:20.125 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:45:20.126 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:45:20.126 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:45:20.126 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:45:20.126 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.126 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:20.127 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:45:20.128 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.128 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:45:20.128 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.128 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:20.129 17495-17621 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@312103c
2025-07-30 05:45:20.129 17495-17621 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@a0b61c5
2025-07-30 05:45:20.129 17495-17621 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:45:20.130 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:45:20.130 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 5a6be461-1c5a-4a72-b831-b5a9dce9b019
2025-07-30 05:45:20.130 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:45:20.130 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 2030edc4-c57a-478d-8469-2972e06facca
2025-07-30 05:45:20.130 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid ddd678e6-5f3e-4ed2-91e2-16aa05226822
2025-07-30 05:45:20.130 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid ca795f06-0758-438e-a049-65ac8cc44ab3
2025-07-30 05:45:20.130 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  D  Trigger dispatch completed. Alerting subscribers.
2025-07-30 05:45:20.131 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.x5 fired: TriggerDispatchCompletedEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832719,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832719453E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.75383270058E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 85
                                                                                                          },
                                                                                                          "time": 1.75383270057E9,
                                                                                                          "session_id": "0c52fd69-1cec-4bfd-b38f-52c74a648c95"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 18
                                                                                                          },
                                                                                                          "time": 1.753832719439E9,
                                                                                                          "session_id": "5b9f6002-3889-49ab-94bf-38c7b176eef1"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.131 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.x5 on 1 subscribers.
2025-07-30 05:45:20.131 17495-17621 Braze v24.....bo.app.d6 org.levimc.launcher                  D  In flight trigger requests is empty. Executing any pending trigger events.
2025-07-30 05:45:20.458 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:20.458 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:20.459 17495-17621 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:20.459 17495-17621 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:20.461 17495-17621 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@312103c
2025-07-30 05:45:20.461 17495-17621 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@a0b61c5
2025-07-30 05:45:20.461 17495-17621 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:45:20.466 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "62d510452c285260"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832720,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:20.838 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 62d510452c285260 time = 371ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:45:20.838 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:45:20.838 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:45:20.838 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:45:20.839 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832720,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:45:20.839 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:45:20.839 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:45:20.839 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832720,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:20.840 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:45:20.841 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832720,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.841 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:45:20.842 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832720,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:20.842 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:20.842 17495-17621 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@312103c
2025-07-30 05:45:20.843 17495-17621 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@a0b61c5
2025-07-30 05:45:20.843 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:45:20.844 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:45:22.541 17495-17602 fmod                    org.levimc.launcher                  I  AudioDevice::init : Min buffer size: 8224 bytes
2025-07-30 05:45:22.542 17495-17602 fmod                    org.levimc.launcher                  I  AudioDevice::init : Actual buffer size: 8224 bytes
2025-07-30 05:45:22.560 17495-17602 AudioTrack              org.levimc.launcher                  W  Use of stream types is deprecated for operations other than volume control
2025-07-30 05:45:22.560 17495-17602 AudioTrack              org.levimc.launcher                  W  See the documentation of AudioTrack() for what to use instead with android.media.AudioAttributes to qualify your playback use case
2025-07-30 05:45:22.688 17495-17495 XALJAVA                 org.levimc.launcher                  V  [PresenceManager] XalLogger created.
2025-07-30 05:45:22.695 17495-17495 XALJAVA                 org.levimc.launcher                  W  [P][PresenceManager] Ignoring resume, not currently paused
2025-07-30 05:45:22.701 17495-17495 HttpCallStaticGlue      org.levimc.launcher                  D  Successfully registerered HttpCall methods
2025-07-30 05:45:22.702 17495-17495 XboxLiveAppConfig       org.levimc.launcher                  D  Successfully registerered XboxLiveAppConfig methods
2025-07-30 05:45:22.702 17495-17495 XSAPI.Android           org.levimc.launcher                  D  Successfully registerered HttpCall tcuiMethods
2025-07-30 05:45:22.705 17495-17495 Interop                 org.levimc.launcher                  I  locale is: en_GB
2025-07-30 05:45:22.755 17495-17495 Minecraft               org.levimc.launcher                  E  Unable to get Firebase Messaging token, trying again...
2025-07-30 05:45:22.772 17495-17614 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLProvider;-><init>()V (runtime_flags=CorePlatformApi, domain=core-platform, api=unsupported,core-platform-api) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-30 05:45:22.774 17495-17614 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLRandom;-><init>()V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-30 05:45:22.830 17495-17602 LicenseChecker          org.levimc.launcher                  I  Binding to licensing service.
2025-07-30 05:45:22.833 17495-17602 MinecraftL...erCallback org.levimc.launcher                  I  error: 6
2025-07-30 05:45:22.870 17495-17602 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Async Services Manager starting in Legacy mode
2025-07-30 05:45:23.167 17495-17602 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using External dir (NEW) - CurrentFileStoragePath is now '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-30 05:45:23.183 17495-17602 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-30 05:45:25.475 17495-17602 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Time played notifier not required for 'en_GB'
2025-07-30 05:45:29.484 17495-17643 Braze v24....eCoroutine org.levimc.launcher                  D  Requesting data flush on internal session close flush timer.
2025-07-30 05:45:29.487 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-30 05:45:29.490 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:29.490 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:29.491 17495-17623 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:29.492 17495-17643 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:29.498 17495-17643 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@312103c
2025-07-30 05:45:29.500 17495-17643 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@a0b61c5
2025-07-30 05:45:29.501 17495-17643 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:45:29.504 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-30 05:45:29.505 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@b79d09e
2025-07-30 05:45:29.507 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "85caf33127562900"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832729,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:29.585 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-30 05:45:29.609 17495-17607 SwappyDisplayManager    org.levimc.launcher                  I  Terminating looper thread
2025-07-30 05:45:29.611 17495-17495 GameActivity            org.levimc.launcher                  D  ************** mainWorkCallback *********
2025-07-30 05:45:29.624 17495-17495 MinecraftPE             org.levimc.launcher                  D  onPause
2025-07-30 05:45:29.625 17495-17495 Braze v24....ageManager org.levimc.launcher                  V  Unregistering InAppMessageManager from activity: com.mojang.minecraftpe.Launcher
2025-07-30 05:45:29.632 17495-17540 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:45:29.657 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:29.658 17495-17495 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-30 05:45:29.820 17495-17621 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 85caf33127562900 time = 313ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:45:29.821 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:45:29.821 17495-17621 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:45:29.821 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:45:29.822 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832729,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:45:29.823 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:45:29.823 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:45:29.824 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832729,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:45:29.825 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:45:29.826 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:45:29.826 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:29.826 17495-17621 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:29.826 17495-17621 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:45:29.827 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832729,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:29.828 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:45:29.829 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832729,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:29.829 17495-17621 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:29.830 17495-17621 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@312103c
2025-07-30 05:45:29.830 17495-17621 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@a0b61c5
2025-07-30 05:45:29.831 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:45:29.831 17495-17621 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:45:29.843 17495-17495 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=188
2025-07-30 05:45:30.056 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-30 05:45:30.057 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-30 05:45:30.070 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  onWindowVisibilityChanged(8) false com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ........ 0,0-2340,1080} of VRI[Launcher]@b79d09e
2025-07-30 05:45:30.070 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 Changes: creating=false format=false size=false visible=true alpha=false hint=false visible=true left=false top=false z=false attached=true lifecycleStrategy=false
2025-07-30 05:45:30.070 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 Cur surface: Surface(name=null mNativeObject=-5476376627790659584)/@0x9769f68
2025-07-30 05:45:30.071 17495-17495 SurfaceView             org.levimc.launcher                  I  251191725 surfaceDestroyed
2025-07-30 05:45:30.071 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  surfaceDestroyed callback.size 1 #2 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ........ 0,0-2340,1080}
2025-07-30 05:45:30.103 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  updateSurface: mVisible = false mSurface.isValid() = true
2025-07-30 05:45:30.104 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  releaseSurfaces: viewRoot = VRI[Launcher]@b79d09e
2025-07-30 05:45:30.104 17495-17495 SurfaceView             org.levimc.launcher                  V  Layout: x=0 y=0 w=2340 h=1080, frame=Rect(0, 0 - 2340, 1080)
2025-07-30 05:45:30.105 17495-17524 SurfaceView             org.levimc.launcher                  D  214105008 windowPositionLost, frameNr = 0
2025-07-30 05:45:30.106 17495-17524 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-30 05:45:30.139 17495-17545 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:3,s:0) destructor()
2025-07-30 05:45:30.139 17495-17545 BufferQueueConsumer     org.levimc.launcher                  D  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4(BLAST Consumer)4](id:445700000004,api:0,p:-1,c:17495) disconnect
2025-07-30 05:45:30.141 17495-17495 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.relayoutWindow:11304, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2340), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:45:30.173 17495-17495 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@b79d09e#3](f:0,a:3,s:0) destructor()
2025-07-30 05:45:30.173 17495-17495 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[Launcher]@b79d09e#3(BLAST Consumer)3](id:445700000003,api:0,p:-1,c:17495) disconnect
2025-07-30 05:45:30.175 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=56 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-30 05:45:30.176 17495-17495 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-30 05:45:30.176 17495-17495 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-30 05:45:30.177 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  windowStopped(true) false com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{ef8e1ad V.E...... ........ 0,0-2340,1080} of VRI[Launcher]@b79d09e
2025-07-30 05:45:30.177 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  D  updateSurface: surface is not valid
2025-07-30 05:45:30.177 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  releaseSurfaces: viewRoot = VRI[Launcher]@b79d09e
2025-07-30 05:45:30.177 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-30 05:45:30.178 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  D  updateSurface: surface is not valid
2025-07-30 05:45:30.178 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  releaseSurfaces: viewRoot = VRI[Launcher]@b79d09e
2025-07-30 05:45:30.178 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-30 05:45:30.179 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-30 05:45:30.179 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  Pending transaction will not be applied in sync with a draw due to view not visible
2025-07-30 05:45:30.181 17495-17524 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(40)
2025-07-30 05:45:30.184 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  D  updateSurface: surface is not valid
2025-07-30 05:45:30.184 17495-17495 SurfaceView@ef8e1ad     org.levimc.launcher                  I  releaseSurfaces: viewRoot = VRI[Launcher]@b79d09e
2025-07-30 05:45:30.184 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-30 05:45:30.185 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  I  stopped(true) old = false
2025-07-30 05:45:30.185 17495-17495 VRI[Launcher]@b79d09e   org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/com.mojang.minecraftpe.Launcher set to true
2025-07-30 05:45:30.190 17495-17495 MinecraftPE             org.levimc.launcher                  D  onStop
2025-07-30 05:45:30.192 17495-17623 Braze v24.3.0 .q        org.levimc.launcher                  D  Messaging session stopped. Adding new messaging session timestamp: 1753832730
2025-07-30 05:45:30.193 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Closed session with activity: com.mojang.minecraftpe.Launcher
2025-07-30 05:45:30.193 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9
2025-07-30 05:45:30.193 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  I  Session [5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9] being sealed because its end time is over the grace period. Session: 
                                                                                                    MutableSession(sessionId=5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9, startTime=1.753832719453E9, endTime=1.753832719478E9, isSealed=false, duration=0)
2025-07-30 05:45:30.194 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.f5 fired: SessionSealedEvent(sealedSession=
                                                                                                    MutableSession(sessionId=5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9, startTime=1.753832719453E9, endTime=1.753832730193E9, isSealed=true, duration=10))
2025-07-30 05:45:30.194 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.f5 on 1 subscribers.
2025-07-30 05:45:30.196 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding session id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 10
                                                                                                      },
                                                                                                      "time": 1.753832730194E9,
                                                                                                      "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                    }
2025-07-30 05:45:30.196 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 10
                                                                                                      },
                                                                                                      "time": 1.753832730194E9,
                                                                                                      "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                    }
2025-07-30 05:45:30.200 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 10
                                                                                                      },
                                                                                                      "time": 1.753832730194E9,
                                                                                                      "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                    }
2025-07-30 05:45:30.201 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 7749b94e-01ce-42a3-9025-951345b2777c
2025-07-30 05:45:30.202 17495-17495 MinecraftPE             org.levimc.launcher                  D  onDestroy
2025-07-30 05:45:30.202 17495-17495 System.out              org.levimc.launcher                  I  onDestroy
2025-07-30 05:45:30.206 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"se","data":{"d":10},"time":1.753832730194E9,"session_id":"5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:45:30.206 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:30.208 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9', eventType='SESSION_ENDED'}'
2025-07-30 05:45:30.208 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:45:30.209 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  I  New session created with ID: 7a70e47f-16f8-4491-bf61-e3ad7a516c62
2025-07-30 05:45:30.209 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.d5 fired: SessionCreatedEvent(session=
                                                                                                    MutableSession(sessionId=7a70e47f-16f8-4491-bf61-e3ad7a516c62, startTime=1.753832730208E9, endTime=null, isSealed=false, duration=-1))
2025-07-30 05:45:30.209 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.d5 on 2 subscribers.
2025-07-30 05:45:30.209 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Session start event for new session received.
2025-07-30 05:45:30.210 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 7a70e47f-16f8-4491-bf61-e3ad7a516c62
2025-07-30 05:45:30.210 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 7a70e47f-16f8-4491-bf61-e3ad7a516c62
2025-07-30 05:45:30.210 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding user id to event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753832730209E9,
                                                                                                      "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                    }
2025-07-30 05:45:30.210 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753832730209E9,
                                                                                                      "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                    }
2025-07-30 05:45:30.212 17495-17642 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 7ed37a6f-1751-4df0-83ef-f37b2f29696b
2025-07-30 05:45:30.215 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"ss","data":{},"time":1.753832730209E9,"session_id":"7a70e47f-16f8-4491-bf61-e3ad7a516c62"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-30 05:45:30.215 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:30.219 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = FLUSH_PENDING_BRAZE_EVENTS
                                                                                                    brazeEvent = null
                                                                                                    sessionId = 7a70e47f-16f8-4491-bf61-e3ad7a516c62
                                                                                                    brazeRequest = null
2025-07-30 05:45:30.219 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:30.219 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: true
2025-07-30 05:45:30.219 17495-17623 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:45:30.220 17495-17623 Braze v24.3.0 .l0       org.levimc.launcher                  V  Device object cache cleared.
2025-07-30 05:45:30.220 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting trigger refresh.
2025-07-30 05:45:30.225 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:30.226 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:30.226 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.y5 fired: TriggerDispatchStartedEvent(request={
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.227 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.y5 on 1 subscribers.
2025-07-30 05:45:30.228 17495-17623 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:30.228 17495-17623 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:45:30.228 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting Braze Geofence refresh on session created event due to configuration.
2025-07-30 05:45:30.228 17495-17642 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:30.229 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Content Card refresh on session created event due to server configuration.
2025-07-30 05:45:30.229 17495-17623 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Feature Flags refresh on session created event due to server configuration.
2025-07-30 05:45:30.229 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='7a70e47f-16f8-4491-bf61-e3ad7a516c62', eventType='SESSION_STARTED'}'
2025-07-30 05:45:30.229 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-30 05:45:30.230 17495-17642 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@312103c
2025-07-30 05:45:30.231 17495-17642 Braze v24.3.0 .l0       org.levimc.launcher                  V  Sending full device due to NOTIFICATIONS_ENABLED true
2025-07-30 05:45:30.231 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Creating a session seal alarm with a delay of 10000 ms
2025-07-30 05:45:30.231 17495-17642 Braze v24.3.0 .l0       org.levimc.launcher                  V  Remote Notification setting changed to true. Updating user subscription.
2025-07-30 05:45:30.232 17495-17642 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-30 05:45:30.232 17495-17642 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@a0b61c5
2025-07-30 05:45:30.232 17495-17642 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-30 05:45:30.233 17495-17642 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753832730209E9,"session_id":"7a70e47f-16f8-4491-bf61-e3ad7a516c62"} with uid: 7ed37a6f-1751-4df0-83ef-f37b2f29696b
2025-07-30 05:45:30.233 17495-17642 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":10},"time":1.753832730194E9,"session_id":"5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"} with uid: 7749b94e-01ce-42a3-9025-951345b2777c
2025-07-30 05:45:30.234 17495-17642 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_internal_sdk_metadata'. Using default value '[]'.
2025-07-30 05:45:30.234 17495-17642 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_internal_sdk_metadata' and value: '[]'
2025-07-30 05:45:30.234 17495-17642 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_sdk_metadata'. Using default value '[]'.
2025-07-30 05:45:30.234 17495-17642 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:45:30.235 17495-17642 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-30 05:45:30.235 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.i5 fired: bo.app.i5@adb222f
2025-07-30 05:45:30.235 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.i5 on 1 subscribers.
2025-07-30 05:45:30.236 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-30 05:45:30.236 17495-17623 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-30 05:45:30.236 17495-17623 Braze v24.3.0 .u        org.levimc.launcher                  D  Closed session with id 7a70e47f-16f8-4491-bf61-e3ad7a516c62
2025-07-30 05:45:30.237 17495-17623 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-30 05:45:30.237 17495-17623 Braze v24.3.0 .a5       org.levimc.launcher                  V  Not allowing server config info unlock. Returning null.
2025-07-30 05:45:30.241 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:30.241 17495-17623 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-30 05:45:30.242 17495-17623 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {}
                                                                                                    }
2025-07-30 05:45:30.243 17495-17623 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Not requesting geofences.
2025-07-30 05:45:30.247 17495-17642 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-30 05:45:30.248 17495-17642 Braze v24.3.0 .l0       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:45:30.249 17495-17642 Braze v24.3.0 .m6       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-30 05:45:30.249 17495-17642 Braze v24.3.0 .t0       org.levimc.launcher                  D  Short circuiting execution of network request and immediately marking it as succeeded.
2025-07-30 05:45:30.249 17495-17642 Braze v24.3.0 .j0       org.levimc.launcher                  D  DataSyncRequest executed successfully.
2025-07-30 05:45:30.250 17495-17642 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "respond_with": {}
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.250 17495-17642 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:30.252 17495-17643 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "d550b3307ccf066a"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "X-Braze-TriggersRequest" => "true"
                                                                                                    "X-Braze-DataRequest" => "true"
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-30 05:45:30.270 17495-17602 AudioTrack              org.levimc.launcher                  D  stop(3935): called with 184832 frames delivered
2025-07-30 05:45:30.274 17495-17602 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was lost
2025-07-30 05:45:30.280 17495-17615 libEGL                  org.levimc.launcher                  E  call to OpenGL ES API with no current context (logged once per thread)
2025-07-30 05:45:30.528 17495-17643 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = d550b3307ccf066a time = 275ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-30 05:45:30.528 17495-17643 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-30 05:45:30.528 17495-17643 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-30 05:45:30.528 17495-17643 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-30 05:45:30.529 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-30 05:45:30.530 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-30 05:45:30.530 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-30 05:45:30.531 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-30 05:45:30.532 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-30 05:45:30.532 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-30 05:45:30.532 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-30 05:45:30.532 17495-17643 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 
2025-07-30 05:45:30.533 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.534 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-30 05:45:30.535 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.535 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-30 05:45:30.536 17495-17643 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@312103c
2025-07-30 05:45:30.536 17495-17643 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@a0b61c5
2025-07-30 05:45:30.537 17495-17643 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-30 05:45:30.537 17495-17643 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-30 05:45:30.537 17495-17643 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-30 05:45:30.537 17495-17643 Braze v24.3.0 .j0       org.levimc.launcher                  D  Trigger dispatch completed. Alerting subscribers.
2025-07-30 05:45:30.538 17495-17620 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 7ed37a6f-1751-4df0-83ef-f37b2f29696b
2025-07-30 05:45:30.538 17495-17620 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 7749b94e-01ce-42a3-9025-951345b2777c
2025-07-30 05:45:30.538 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.x5 fired: TriggerDispatchCompletedEvent(request={
                                                                                                      "device_id": "d944b9ac-b116-49c0-89c2-82a3fdeca307",
                                                                                                      "time": 1753832730,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "unknown",
                                                                                                      "app_version_code": "18164407.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753832730209E9,
                                                                                                          "session_id": "7a70e47f-16f8-4491-bf61-e3ad7a516c62"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 10
                                                                                                          },
                                                                                                          "time": 1.753832730194E9,
                                                                                                          "session_id": "5e1d5b3e-1ca1-44e4-a25d-cf89dcd293e9"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-30 05:45:30.538 17495-17643 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.x5 on 1 subscribers.
2025-07-30 05:45:30.538 17495-17643 Braze v24.....bo.app.d6 org.levimc.launcher                  D  In flight trigger requests is empty. Executing any pending trigger events.
2025-07-30 05:45:30.838 17495-17602 fmod                    org.levimc.launcher                  I  AudioDevice::init : Min buffer size: 8224 bytes
2025-07-30 05:45:30.838 17495-17602 fmod                    org.levimc.launcher                  I  AudioDevice::init : Actual buffer size: 8224 bytes
2025-07-30 05:45:30.849 17495-17602 AudioTrack              org.levimc.launcher                  W  Use of stream types is deprecated for operations other than volume control
2025-07-30 05:45:30.849 17495-17602 AudioTrack              org.levimc.launcher                  W  See the documentation of AudioTrack() for what to use instead with android.media.AudioAttributes to qualify your playback use case
2025-07-30 05:45:30.993 17495-17602 AudioTrack              org.levimc.launcher                  D  stop(3936): called with 2560 frames delivered
2025-07-30 05:45:31.183 17495-17615 libMEOW                 org.levimc.launcher                  D  meow delete tls: 0xb4000078acec1400
2025-07-30 05:45:31.185 17495-17602 Minecraft               org.levimc.launcher                  I  Application shutdown successfully
2025-07-30 05:45:31.188 17495-17602 libMEOW                 org.levimc.launcher                  D  meow delete tls: 0xb4000078eae54640
2025-07-30 05:45:31.189 17495-17495 levimc.launcher         org.levimc.launcher                  I  System.exit called, status: 0
2025-07-30 05:45:31.189 17495-17495 AndroidRuntime          org.levimc.launcher                  I  VM exiting with result code 0, cleanup skipped.
---------------------------- PROCESS ENDED (17495) for package org.levimc.launcher ----------------------------