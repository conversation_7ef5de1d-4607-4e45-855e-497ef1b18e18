---------------------------- PROCESS STARTED (19775) for package org.levimc.launcher ----------------------------
2025-07-30 05:49:55.058 19775-19775 nativeloader            org.levimc.launcher                  D  Configuring clns-7 for other apk /data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/lib/arm64:/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/org.levimc.launcher
2025-07-30 05:49:55.061 19775-19775 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 202956589; UID 10645; state: ENABLED
2025-07-30 05:49:55.072 19775-19775 GraphicsEnvironment     org.levimc.launcher                  V  Currently set values for:
2025-07-30 05:49:55.072 19775-19775 GraphicsEnvironment     org.levimc.launcher                  V    angle_gl_driver_selection_pkgs=[]
2025-07-30 05:49:55.072 19775-19775 GraphicsEnvironment     org.levimc.launcher                  V    angle_gl_driver_selection_values=[]
2025-07-30 05:49:55.072 19775-19775 GraphicsEnvironment     org.levimc.launcher                  V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-07-30 05:49:55.072 19775-19775 GraphicsEnvironment     org.levimc.launcher                  V  Neither updatable production driver nor prerelease driver is supported.
2025-07-30 05:49:55.125 19775-19775 FirebaseApp             org.levimc.launcher                  I  Device unlocked: initializing all Firebase APIs for app [DEFAULT]
2025-07-30 05:49:55.147 19775-19775 FirebaseInitProvider    org.levimc.launcher                  I  FirebaseApp initialization successful
2025-07-30 05:49:55.156 19775-19775 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 279646685; UID 10645; state: ENABLED
2025-07-30 05:49:55.168 19775-19794 levimc.launcher         org.levimc.launcher                  W  ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar*393752484]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[])
2025-07-30 05:49:55.176 19775-19775 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 05:49:55.192 19775-19775 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:49:55.195 19775-19794 DynamiteModule          org.levimc.launcher                  I  Considering local module com.google.android.gms.measurement.dynamite:117 and remote module com.google.android.gms.measurement.dynamite:154
2025-07-30 05:49:55.195 19775-19794 DynamiteModule          org.levimc.launcher                  I  Selected remote version of com.google.android.gms.measurement.dynamite, version >= 154
2025-07-30 05:49:55.195 19775-19794 DynamiteModule          org.levimc.launcher                  V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
2025-07-30 05:49:55.198 19775-19804 HWUI                    org.levimc.launcher                  D  CacheManager constructor. deviceInfo=(1080, 1920)
2025-07-30 05:49:55.198 19775-19804 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000079ee4f2e40
2025-07-30 05:49:55.198 19775-19804 libMEOW                 org.levimc.launcher                  D  meow reload base cfg path: na
2025-07-30 05:49:55.198 19775-19804 libMEOW                 org.levimc.launcher                  D  meow reload overlay cfg path: na
2025-07-30 05:49:55.198 19775-19804 QT                      org.levimc.launcher                  W  qt_process_init() called
2025-07-30 05:49:55.198 19775-19804 QT                      org.levimc.launcher                  E  [QT]file does not exist
2025-07-30 05:49:55.198 19775-19804 QT                      org.levimc.launcher                  W  Support!!
2025-07-30 05:49:55.199 19775-19804 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-30 05:49:55.238 19775-19794 System                  org.levimc.launcher                  W  ClassLoader referenced unknown path: 
2025-07-30 05:49:55.239 19775-19794 nativeloader            org.levimc.launcher                  D  Configuring clns-8 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~9IMcW4SO0wxftayC0Bi-qQ==/com.google.android.gms-EK-bbzbHMJeI1wyO0dJG3Q==/lib/arm64:/data/app/~~9IMcW4SO0wxftayC0Bi-qQ==/com.google.android.gms-EK-bbzbHMJeI1wyO0dJG3Q==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-07-30 05:49:55.252 19775-19794 levimc.launcher         org.levimc.launcher                  W  ClassLoaderContext classpath element checksum mismatch. expected=**********, found=838285025 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar*393752484]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk*838285025])
2025-07-30 05:49:55.320 19775-19775 AppCompatDelegate       org.levimc.launcher                  D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-07-30 05:49:55.372 19775-19775 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 309578419; UID 10645; state: ENABLED
2025-07-30 05:49:55.385 19775-19775 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@e304d5d
2025-07-30 05:49:55.394 19775-19775 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-30 05:49:55.438 19775-19819 levimc.launcher         org.levimc.launcher                  E  No package ID 6a found for resource ID 0x6a0b000f.
2025-07-30 05:49:55.455 19775-19819 FA                      org.levimc.launcher                  I  App measurement initialized, version: 132006
2025-07-30 05:49:55.455 19775-19819 FA                      org.levimc.launcher                  I  To enable debug logging run: adb shell setprop log.tag.FA VERBOSE
2025-07-30 05:49:55.456 19775-19819 FA                      org.levimc.launcher                  I  To enable faster debug mode event logging run:
                                                                                                      adb shell setprop debug.firebase.analytics.app org.levimc.launcher
2025-07-30 05:49:55.457 19775-19775 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 63938206; UID 10645; state: ENABLED
2025-07-30 05:49:55.497 19775-19819 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:49:55.499 19775-19775 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 352594277; UID 10645; state: ENABLED
2025-07-30 05:49:55.510 19775-19775 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-30 05:49:55.511 19775-19775 HardwareRenderer        org.levimc.launcher                  D  onDisplayChanged. displayId=0 current wxh=2340x1080 mLargest wxh=0x0
2025-07-30 05:49:55.511 19775-19775 HWUI                    org.levimc.launcher                  W  Unknown dataspace 0
2025-07-30 05:49:55.511 19775-19804 HWUI                    org.levimc.launcher                  D  setMaxSurfaceArea requested wxh=(2340,1080) requestedSurfaceArea(2527200) mMaxSurfaceArea(2073600)
2025-07-30 05:49:55.511 19775-19775 HardwareRenderer        org.levimc.launcher                  D  Set largestWidth and largestHeight as logical resolution. (2340x1080)
2025-07-30 05:49:55.512 19775-19804 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-30 05:49:55.516 19775-19775 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars navigationBars captionBar, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-30 05:49:55.524 19775-19775 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'eaefd38', fd=143
2025-07-30 05:49:55.525 19775-19775 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:49:55.527 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-30 05:49:55.527 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@5834b63 IsHRR=false TM=true
2025-07-30 05:49:55.528 19775-19775 IDS_TAG                 org.levimc.launcher                  I  Starting IDS observe window
2025-07-30 05:49:55.528 19775-19775 IDS_TAG                 org.levimc.launcher                  I  Getting Shared Preference for android.app.Application@10798a7 uid = 10645
2025-07-30 05:49:55.529 19775-19775 IDS_TAG                 org.levimc.launcher                  I  App android.app.Application@10798a7 has not finished training
2025-07-30 05:49:55.534 19775-19775 IDS_TAG                 org.levimc.launcher                  I  Closing IDS observe window
2025-07-30 05:49:55.535 19775-19775 IDS_TAG                 org.levimc.launcher                  I  Getting Shared Preference for android.app.Application@10798a7 uid = 10645
2025-07-30 05:49:55.535 19775-19775 IDS_TAG                 org.levimc.launcher                  I  IDS count updated to 2 for android.app.Application@10798a7
2025-07-30 05:49:55.552 19775-19819 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:49:55.556 19775-19819 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:49:55.558 19775-19819 FA                      org.levimc.launcher                  I  Tag Manager is not found and thus will not be used
2025-07-30 05:49:55.571 19775-19775 BufferQueueConsumer     org.levimc.launcher                  D  [](id:4d3f00000000,api:0,p:-1,c:19775) connect: controlledByApp=false
2025-07-30 05:49:55.575 19775-19775 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@1df8348#0](f:0,a:0,s:0) constructor()
2025-07-30 05:49:55.575 19775-19775 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[SplashActivity]@1df8348 mNativeObject= 0xb400007a21fde400 sc.mNativeObject= 0xb4000079890a6780 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-30 05:49:55.575 19775-19775 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[SplashActivity]@1df8348 mNativeObject= 0xb400007a21fde400 sc.mNativeObject= 0xb4000079890a6780 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-30 05:49:55.575 19775-19775 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@1df8348#0](f:0,a:0,s:0) update width=2340 height=1080 format=-1 mTransformHint=4
2025-07-30 05:49:55.577 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=19 res=0x3 s={true 0xb4000079e59ba000} ch=true seqId=0
2025-07-30 05:49:55.578 19775-19819 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:49:55.578 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-30 05:49:55.582 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079e59ba000} hwInitialized=true
2025-07-30 05:49:55.586 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-30 05:49:55.587 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[SplashActivity]@1df8348#0
2025-07-30 05:49:55.587 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Creating new active sync group VRI[SplashActivity]@1df8348#1
2025-07-30 05:49:55.589 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:49:55.602 19775-19830 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-30 05:49:55.602 19775-19830 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  mWNT: t=0xb400007989361d80 mBlastBufferQueue=0xb400007a21fde400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-30 05:49:55.602 19775-19830 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:49:55.607 19775-19804 levimc.launcher         org.levimc.launcher                  D  Can't load libmbrainSDK
2025-07-30 05:49:55.617 19775-19804 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@1df8348#0](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-30 05:49:55.617 19775-19804 GrallocExtra            org.levimc.launcher                  I  gralloc_extra_query:is_SW3D 0
2025-07-30 05:49:55.617 19775-19804 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@1df8348#0](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=293792585417818(auto) mPendingTransactions.size=0 graphicBufferId=84932978278406 transform=7
2025-07-30 05:49:55.618 19775-19804 levimc.launcher         org.levimc.launcher                  D  Can't load libmbrainSDK
2025-07-30 05:49:55.618 19775-19804 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-30 05:49:55.620 19775-19804 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 19775    Tid : 19804
2025-07-30 05:49:55.620 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:49:55.623 19775-19804 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-30 05:49:55.625 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-30 05:49:55.645 19775-19775 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:49:55.648 19775-19775 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=statusBars navigationBars, animType=1, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-30 05:49:55.649 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-30 05:49:55.649 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-30 05:49:55.649 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-30 05:49:55.655 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[SplashActivity]@1df8348#2
2025-07-30 05:49:55.655 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Creating new active sync group VRI[SplashActivity]@1df8348#3
2025-07-30 05:49:55.658 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-30 05:49:55.659 19775-19830 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=8 frameNum=4.
2025-07-30 05:49:55.659 19775-19830 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-30 05:49:55.671 19775-19775 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e59ba000}
2025-07-30 05:49:55.672 19775-19775 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-30 05:49:55.673 19775-19775 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-30 05:49:55.673 19775-19804 VRI[Splash...y]@1df8348 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=4 didProduceBuffer=true
2025-07-30 05:49:55.673 19775-19804 VRI[Splash...y]@1df8348 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-30 05:49:55.680 19775-19786 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=175
2025-07-30 05:49:55.735 19775-19775 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-30 05:49:55.736 19775-19775 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity
2025-07-30 05:49:56.018 19775-19834 InteractionJankMonitor  org.levimc.launcher                  W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=org.levimc.launcher
2025-07-30 05:49:56.031 19775-19775 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=statusBars navigationBars, animType=1, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-30 05:49:56.032 19775-19779 levimc.launcher         org.levimc.launcher                  I  Compiler allocated 4280KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
2025-07-30 05:49:56.289 19775-19779 levimc.launcher         org.levimc.launcher                  I  Compiler allocated 7416KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-30 05:49:57.592 19775-19819 FA                      org.levimc.launcher                  W  Failed to retrieve Firebase Instance Id
2025-07-30 05:49:57.594 19775-19775 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-30 05:49:57.618 19775-19775 nativeloader            org.levimc.launcher                  D  Load /data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk!/lib/arm64-v8a/libleviutils.so using class loader ns clns-7 (caller=/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk!classes9.dex): dlopen failed: "/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk!/lib/arm64-v8a/libleviutils.so" is 32-bit instead of 64-bit
2025-07-30 05:49:57.618 19775-19775 AndroidRuntime          org.levimc.launcher                  D  Shutting down VM
2025-07-30 05:49:57.619 19775-19775 AndroidRuntime          org.levimc.launcher                  E  FATAL EXCEPTION: main
                                                                                                    Process: org.levimc.launcher, PID: 19775
                                                                                                    java.lang.UnsatisfiedLinkError: dlopen failed: "/data/app/~~fEtzp5EXY3959rN9wbDoig==/org.levimc.launcher-9E3uIrD0V0eYXLapv0OGHg==/base.apk!/lib/arm64-v8a/libleviutils.so" is 32-bit instead of 64-bit
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1090)
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1012)
                                                                                                    	at java.lang.System.loadLibrary(System.java:1765)
                                                                                                    	at org.levimc.launcher.ui.activities.MainActivity.<clinit>(MainActivity.java:59)
                                                                                                    	at java.lang.Class.newInstance(Native Method)
                                                                                                    	at android.app.AppComponentFactory.instantiateActivity(AppComponentFactory.java:95)
                                                                                                    	at androidx.core.app.CoreComponentFactory.instantiateActivity(CoreComponentFactory.java:44)
                                                                                                    	at android.app.Instrumentation.newActivity(Instrumentation.java:1448)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4325)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:107)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-30 05:49:57.630 19775-19775 Process                 org.levimc.launcher                  I  Sending signal. PID: 19775 SIG: 9
---------------------------- PROCESS ENDED (19775) for package org.levimc.launcher ----------------------------
